@startuml Sequence_Kelola_User
title Sequence Diagram - Kelola User (Admin Only, Include Audit Trail)

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor Admin
participant ":UserManagementPage" as UMP
participant ":AdminController" as AC
participant ":UserController" as UC
participant ":AuditController" as AuC
participant ":Database" as DB

Admin -> UMP: 1. accessUserManagement()
activate UMP

UMP -> AC: 2. checkAdminRole()
activate AC

AC -> DB: 2.1. validateAdminRole()
activate DB
DB --> AC: 2.2. roleConfirmed()
deactivate DB

alt notAdmin
    AC --> UMP: 2.3. accessDenied()
    UMP --> Admin: 2.4. showAccessError()
else isAdmin
    AC -> UC: 2.5. loadUserList()
    activate UC
    
    UC -> DB: 2.6. getAllUsers()
    activate DB
    DB --> UC: 2.7. userList()
    deactivate DB
    
    UC --> AC: 2.8. displayUserList()
    AC --> UMP: 2.9. showUserManagement()
    UMP --> Admin: 2.10. displayUserInterface()
    
    Admin -> UMP: 3. performUserAction(action, userData)
    
    UMP -> UC: 4. executeUserAction(action, userData)
    
    alt addUser
        UC -> DB: 4.1. createNewUser(userData)
        activate DB
        DB --> UC: 4.2. userCreated()
        deactivate DB
        
        UC -> AuC: 4.3. logUserCreation(userData)
        activate AuC
        note right: Include relationship
        
        AuC -> DB: 4.4. saveAuditLog(createUserEvent)
        activate DB
        DB --> AuC: 4.5. auditSaved()
        deactivate DB
        
        AuC --> UC: 4.6. auditCompleted()
        deactivate AuC
        
    else updateUser
        UC -> DB: 4.7. updateUserData(userData)
        activate DB
        DB --> UC: 4.8. userUpdated()
        deactivate DB
        
        UC -> AuC: 4.9. logUserUpdate(userData)
        activate AuC
        
        AuC -> DB: 4.10. saveAuditLog(updateUserEvent)
        activate DB
        DB --> AuC: 4.11. auditSaved()
        deactivate DB
        
        AuC --> UC: 4.12. auditCompleted()
        deactivate AuC
        
    else deleteUser
        UC -> DB: 4.13. deleteUser(userId)
        activate DB
        DB --> UC: 4.14. userDeleted()
        deactivate DB
        
        UC -> AuC: 4.15. logUserDeletion(userId)
        activate AuC
        
        AuC -> DB: 4.16. saveAuditLog(deleteUserEvent)
        activate DB
        DB --> AuC: 4.17. auditSaved()
        deactivate DB
        
        AuC --> UC: 4.18. auditCompleted()
        deactivate AuC
    end
    
    UC --> AC: 4.19. actionCompleted()
    AC --> UMP: 4.20. showActionResult()
    UMP --> Admin: 4.21. displaySuccessMessage()
    
    deactivate UC
end

deactivate AC
deactivate UMP

@enduml
