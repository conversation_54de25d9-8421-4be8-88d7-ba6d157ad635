<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
  <bpmn2:collaboration id="Collaboration_1">
    <bpmn2:participant id="Participant_User" name="Pengguna" processRef="Process_User" />
    <bpmn2:participant id="Participant_System" name="Sistem" processRef="Process_System" />
    <bpmn2:messageFlow id="MessageFlow_1" sourceRef="Task_EnterCredentials" targetRef="Task_ValidateLogin" />
    <bpmn2:messageFlow id="MessageFlow_2" sourceRef="Task_ShowDashboard" targetRef="Task_UseSystem" />
  </bpmn2:collaboration>
  
  <bpmn2:process id="Process_User" isExecutable="false">
    <bpmn2:startEvent id="StartEvent_1" name="Mulai Login">
      <bpmn2:outgoing>SequenceFlow_1</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:task id="Task_OpenLogin" name="Buka halaman login">
      <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_EnterCredentials" name="Masukkan username dan password">
      <bpmn2:incoming>SequenceFlow_2</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_3</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_UseSystem" name="Gunakan sistem">
      <bpmn2:incoming>SequenceFlow_3</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_4</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:endEvent id="EndEvent_1" name="Selesai">
      <bpmn2:incoming>SequenceFlow_4</bpmn2:incoming>
    </bpmn2:endEvent>
    <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="Task_OpenLogin" />
    <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="Task_OpenLogin" targetRef="Task_EnterCredentials" />
    <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="Task_EnterCredentials" targetRef="Task_UseSystem" />
    <bpmn2:sequenceFlow id="SequenceFlow_4" sourceRef="Task_UseSystem" targetRef="EndEvent_1" />
  </bpmn2:process>
  
  <bpmn2:process id="Process_System" isExecutable="false">
    <bpmn2:task id="Task_ShowForm" name="Tampilkan form login">
      <bpmn2:outgoing>SequenceFlow_S1</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_ValidateLogin" name="Validasi kredensial">
      <bpmn2:incoming>SequenceFlow_S1</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S2</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:exclusiveGateway id="Gateway_1" name="Valid?">
      <bpmn2:incoming>SequenceFlow_S2</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S3</bpmn2:outgoing>
      <bpmn2:outgoing>SequenceFlow_S4</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:task id="Task_ShowDashboard" name="Tampilkan dashboard">
      <bpmn2:incoming>SequenceFlow_S3</bpmn2:incoming>
    </bpmn2:task>
    <bpmn2:task id="Task_ShowError" name="Tampilkan error">
      <bpmn2:incoming>SequenceFlow_S4</bpmn2:incoming>
    </bpmn2:task>
    <bpmn2:sequenceFlow id="SequenceFlow_S1" sourceRef="Task_ShowForm" targetRef="Task_ValidateLogin" />
    <bpmn2:sequenceFlow id="SequenceFlow_S2" sourceRef="Task_ValidateLogin" targetRef="Gateway_1" />
    <bpmn2:sequenceFlow id="SequenceFlow_S3" name="Ya" sourceRef="Gateway_1" targetRef="Task_ShowDashboard" />
    <bpmn2:sequenceFlow id="SequenceFlow_S4" name="Tidak" sourceRef="Gateway_1" targetRef="Task_ShowError" />
  </bpmn2:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1">
      <bpmndi:BPMNShape id="Participant_User_di" bpmnElement="Participant_User" isHorizontal="true">
        <dc:Bounds x="160" y="80" width="800" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_System_di" bpmnElement="Participant_System" isHorizontal="true">
        <dc:Bounds x="160" y="320" width="800" height="200" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="212" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="195" y="205" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_OpenLogin_di" bpmnElement="Task_OpenLogin">
        <dc:Bounds x="300" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_EnterCredentials_di" bpmnElement="Task_EnterCredentials">
        <dc:Bounds x="450" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_UseSystem_di" bpmnElement="Task_UseSystem">
        <dc:Bounds x="600" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="752" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="750" y="205" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="Task_ShowForm_di" bpmnElement="Task_ShowForm">
        <dc:Bounds x="300" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_ValidateLogin_di" bpmnElement="Task_ValidateLogin">
        <dc:Bounds x="450" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1_di" bpmnElement="Gateway_1" isMarkerVisible="true">
        <dc:Bounds x="595" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="605" y="452" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_ShowDashboard_di" bpmnElement="Task_ShowDashboard">
        <dc:Bounds x="700" y="340" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_ShowError_di" bpmnElement="Task_ShowError">
        <dc:Bounds x="700" y="460" width="100" height="80" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNEdge id="SequenceFlow_1_di" bpmnElement="SequenceFlow_1">
        <di:waypoint x="248" y="180" />
        <di:waypoint x="300" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_2_di" bpmnElement="SequenceFlow_2">
        <di:waypoint x="400" y="180" />
        <di:waypoint x="450" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_3_di" bpmnElement="SequenceFlow_3">
        <di:waypoint x="550" y="180" />
        <di:waypoint x="600" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_4_di" bpmnElement="SequenceFlow_4">
        <di:waypoint x="700" y="180" />
        <di:waypoint x="752" y="180" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="SequenceFlow_S1_di" bpmnElement="SequenceFlow_S1">
        <di:waypoint x="400" y="420" />
        <di:waypoint x="450" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S2_di" bpmnElement="SequenceFlow_S2">
        <di:waypoint x="550" y="420" />
        <di:waypoint x="595" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S3_di" bpmnElement="SequenceFlow_S3">
        <di:waypoint x="620" y="395" />
        <di:waypoint x="620" y="380" />
        <di:waypoint x="700" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="628" y="385" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S4_di" bpmnElement="SequenceFlow_S4">
        <di:waypoint x="620" y="445" />
        <di:waypoint x="620" y="500" />
        <di:waypoint x="700" y="500" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="622" y="470" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="MessageFlow_1_di" bpmnElement="MessageFlow_1">
        <di:waypoint x="500" y="220" />
        <di:waypoint x="500" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="MessageFlow_2_di" bpmnElement="MessageFlow_2">
        <di:waypoint x="750" y="340" />
        <di:waypoint x="750" y="280" />
        <di:waypoint x="650" y="280" />
        <di:waypoint x="650" y="220" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>
