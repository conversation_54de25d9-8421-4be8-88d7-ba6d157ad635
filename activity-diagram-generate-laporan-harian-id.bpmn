<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_GenerateLaporanHarian" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_GenerateLaporanHarian">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_PilihTanggal" targetRef="Task_ValidasiTanggal" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanLaporan" targetRef="Task_LihatLaporan" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Proses Admin" isExecutable="false">
    <bpmn:startEvent id="StartEvent_GenerateLaporan" name="Mulai Generate Laporan">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_BukaMenuLaporan" name="Buka menu laporan harian">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihTanggal" name="Pilih tanggal laporan">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_LihatLaporan" name="Lihat laporan harian">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihTanggalLain" name="Pilih tanggal yang valid">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_GenerateLaporan" name="Selesai">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_GenerateLaporan" targetRef="Task_BukaMenuLaporan" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_BukaMenuLaporan" targetRef="Task_PilihTanggal" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_PilihTanggal" targetRef="Task_LihatLaporan" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_LihatLaporan" targetRef="EndEvent_GenerateLaporan" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_LihatLaporan" targetRef="EndEvent_GenerateLaporan" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_PilihTanggalLain" targetRef="Task_PilihTanggal" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_PilihTanggalLain" targetRef="Task_PilihTanggal" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_TampilkanFormLaporan" name="Tampilkan form laporan">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidasiTanggal" name="Validasi tanggal">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_TanggalValid" name="Tanggal valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_AmbilDataPenjualan" name="Ambil data penjualan harian">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_HitungTotal" name="Hitung total penjualan">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatLaporan" name="Format laporan">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanLaporan" name="Tampilkan laporan harian">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanError" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_TampilkanFormLaporan" targetRef="Task_ValidasiTanggal" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidasiTanggal" targetRef="Gateway_TanggalValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Ya" sourceRef="Gateway_TanggalValid" targetRef="Task_AmbilDataPenjualan" />
    <bpmn:sequenceFlow id="Flow_S4" name="Tidak" sourceRef="Gateway_TanggalValid" targetRef="Task_TampilkanError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_AmbilDataPenjualan" targetRef="Task_HitungTotal" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_HitungTotal" targetRef="Task_FormatLaporan" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_FormatLaporan" targetRef="Task_TampilkanLaporan" />
  </bpmn:process>
</bpmn:definitions>
