@startuml Sequence_Menambahkan_Keranjang
title Sequence Diagram - Menambahkan ke Keranjang

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor Customer
participant ":ProductPage" as PP
participant ":CartController" as CC
participant ":Database" as DB

Customer -> PP: 1. selectProduct(productId)
activate PP

PP -> Customer: 2. displayProductDetails()

Customer -> PP: 3. addToCart(productId, quantity)

PP -> CC: 4. addItemToCart(productId, quantity)
activate CC

CC -> DB: 4.1. checkProductStock(productId)
activate DB
DB --> CC: 4.2. stockAvailable()
deactivate DB

alt stockAvailable
    CC -> DB: 4.3. checkCartItem(productId)
    activate DB
    DB --> CC: 4.4. cartItemStatus()
    deactivate DB

    alt itemExists
        CC -> DB: 4.5. updateCartQuantity(productId, quantity)
        activate DB
        DB --> CC: 4.6. quantityUpdated()
        deactivate DB
    else itemNotExists
        CC -> DB: 4.7. addNewCartItem(productId, quantity)
        activate DB
        DB --> CC: 4.8. itemAdded()
        deactivate DB
    end

    CC --> PP: 4.9. addToCartSuccess()
    PP --> Customer: 4.10. showSuccessMessage()
else stockNotAvailable
    CC --> PP: 4.11. stockInsufficient()
    PP --> Customer: 4.12. showStockError()
end

deactivate CC
deactivate PP

@enduml
