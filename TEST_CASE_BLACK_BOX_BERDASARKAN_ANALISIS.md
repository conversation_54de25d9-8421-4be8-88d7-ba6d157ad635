# 🧪 TEST CASE BLACK BOX TESTING
# SISTEM INFORMASI MANAJEMEN UBI BAKAR CILEMBU
## Berdasarkan Analisis Kebutuhan Fungsional & Use Case

---

## 📋 **INFORMASI DOKUMEN**

| **Atribut** | **Detail** |
|-------------|------------|
| **<PERSON>a <PERSON>** | Sistem Informasi Manajemen Ubi Bakar Cilembu |
| **Testing Method** | Black Box Testing |
| **Basis Analisis** | 10 Kebutuhan Fungsional & 10 Use Case |
| **Fokus Testing** | Input-Output Validation & Business Logic |
| **Tanggal Dibuat** | 27 Juli 2025 |

---

## 🎯 **TEST CASE BLACK BOX - FORMAT TABEL**

| **No** | **Fitur** | **Skenario Test** | **Langkah Test** | **Data Test** | **Hasil** |
|--------|-----------|-------------------|------------------|---------------|-----------|
| 1 | F01 - Login | Login dengan password salah | 1. Buka halaman login<br>2. Input username valid<br>3. Input password salah<br>4. Klik Login | Username: <EMAIL><br>Password: salahpass123 | Muncul notifikasi "Username atau password salah", tetap di halaman login |
| 2 | F01 - Login | Login dengan username kosong | 1. Buka halaman login<br>2. Biarkan username kosong<br>3. Input password<br>4. Klik Login | Username: (kosong)<br>Password: admin123 | Muncul notifikasi "Username wajib diisi", focus ke field username |
| 3 | F01 - Login | Login dengan format email invalid | 1. Buka halaman login<br>2. Input email format salah<br>3. Input password<br>4. Klik Login | Username: admintestcom<br>Password: admin123 | Muncul notifikasi "Format email tidak valid" |
| 4 | F01 - Login | Login dengan user tidak terdaftar | 1. Buka halaman login<br>2. Input email tidak terdaftar<br>3. Input password<br>4. Klik Login | Username: <EMAIL><br>Password: password123 | Muncul notifikasi "User tidak ditemukan" |
| 5 | F01 - Login | Login dengan password kosong | 1. Buka halaman login<br>2. Input username valid<br>3. Biarkan password kosong<br>4. Klik Login | Username: <EMAIL><br>Password: (kosong) | Muncul notifikasi "Password wajib diisi", focus ke field password |
| 6 | F02 - Logout | Logout normal | 1. Login sebagai user valid<br>2. Klik tombol Logout<br>3. Amati response | User: admin yang sudah login | Berhasil logout, redirect ke halaman login, session dihapus |
| 7 | F02 - Logout | Akses halaman setelah logout | 1. Login sebagai user<br>2. Logout<br>3. Akses URL protected langsung | URL: /dashboard setelah logout | Redirect ke halaman login dengan pesan "Silakan login terlebih dahulu" |
| 8 | F03 - Manajemen User | Tambah user dengan email duplikat | 1. Login sebagai admin<br>2. Buka manajemen user<br>3. Tambah user dengan email existing<br>4. Klik Simpan | Name: User Baru<br>Email: <EMAIL> (sudah ada)<br>Password: newpass123 | Muncul notifikasi "Email sudah terdaftar", data tidak tersimpan |
| 9 | F03 - Manajemen User | Tambah user dengan password kurang dari 8 karakter | 1. Login sebagai admin<br>2. Buka manajemen user<br>3. Isi form dengan password pendek<br>4. Klik Simpan | Name: User Test<br>Email: <EMAIL><br>Password: 123 | Muncul notifikasi "Password minimal 8 karakter" |
| 10 | F03 - Manajemen User | Edit user dengan data kosong | 1. Login sebagai admin<br>2. Pilih user untuk edit<br>3. Kosongkan field nama<br>4. Klik Update | Name: (kosong)<br>Email: <EMAIL><br>Role: employee | Muncul notifikasi "Nama tidak boleh kosong" |
| 11 | F03 - Manajemen User | Hapus user yang sedang login | 1. Login sebagai admin<br>2. Buka daftar user<br>3. Hapus user admin yang sedang login<br>4. Konfirmasi hapus | User ID: admin yang sedang aktif | Muncul notifikasi "Tidak dapat menghapus user yang sedang aktif" |
| 12 | F04 - Lihat Laporan | Akses laporan tanpa data | 1. Login sebagai user<br>2. Buka menu laporan<br>3. Pilih laporan transaksi | Database: tidak ada transaksi | Tampil halaman laporan dengan pesan "Data tidak ditemukan" |
| 13 | F04 - Lihat Laporan | Filter laporan dengan tanggal invalid | 1. Login sebagai user<br>2. Buka laporan<br>3. Set tanggal mulai > tanggal akhir<br>4. Klik Filter | Tanggal Mulai: 2025-12-31<br>Tanggal Akhir: 2025-01-01 | Muncul notifikasi "Tanggal akhir harus setelah tanggal mulai" |
| 14 | F05 - Export Laporan | Export laporan kosong ke PDF | 1. Login sebagai user<br>2. Buka laporan kosong<br>3. Klik Export PDF | Laporan: tidak ada data | File PDF ter-generate dengan header dan pesan "Tidak ada data" |
| 15 | F05 - Export Laporan | Export laporan kosong ke Excel | 1. Login sebagai user<br>2. Buka laporan kosong<br>3. Klik Export Excel | Laporan: tidak ada data | File Excel ter-generate dengan header dan sheet kosong |
| 16 | F06 - Tambah Ubi Mentah | Tambah ubi mentah dengan data tidak lengkap | 1. Login sebagai user<br>2. Buka form tambah ubi mentah<br>3. Isi sebagian field<br>4. Klik Simpan | Nama: Ubi Cilembu<br>Supplier: (kosong)<br>Quantity: (kosong)<br>Harga: 15000 | Muncul notifikasi "Field wajib tidak boleh kosong", highlight field kosong |
| 17 | F06 - Tambah Ubi Mentah | Tambah ubi mentah dengan quantity negatif | 1. Login sebagai user<br>2. Buka form tambah ubi mentah<br>3. Input quantity negatif<br>4. Klik Simpan | Nama: Ubi Test<br>Supplier: Petani A<br>Quantity: -10<br>Harga: 15000 | Muncul notifikasi "Quantity tidak boleh negatif" |
| 18 | F06 - Tambah Ubi Mentah | Tambah ubi mentah dengan harga nol | 1. Login sebagai user<br>2. Buka form tambah ubi mentah<br>3. Input harga 0<br>4. Klik Simpan | Nama: Ubi Test<br>Supplier: Petani A<br>Quantity: 100<br>Harga: 0 | Muncul notifikasi "Harga harus lebih dari 0" |
| 19 | F07 - Tambah Ubi Bakar | Tambah ubi bakar dengan harga negatif | 1. Login sebagai user<br>2. Buka form tambah ubi bakar<br>3. Input harga negatif<br>4. Klik Simpan | Nama: Ubi Bakar Test<br>Harga: -5000<br>Stok: 100 | Muncul notifikasi "Harga tidak boleh negatif" |
| 20 | F07 - Tambah Ubi Bakar | Tambah ubi bakar dengan stok negatif | 1. Login sebagai user<br>2. Buka form tambah ubi bakar<br>3. Input stok negatif<br>4. Klik Simpan | Nama: Ubi Bakar Test<br>Harga: 8000<br>Stok: -50 | Muncul notifikasi "Stok tidak boleh negatif" |
| 21 | F07 - Tambah Ubi Bakar | Tambah ubi bakar dengan nama kosong | 1. Login sebagai user<br>2. Buka form tambah ubi bakar<br>3. Kosongkan nama produk<br>4. Klik Simpan | Nama: (kosong)<br>Harga: 8000<br>Stok: 100 | Muncul notifikasi "Nama produk wajib diisi" |
| 22 | F08 - Tambah Produk Lain | Tambah produk dengan nama duplikat | 1. Login sebagai user<br>2. Buka form tambah produk lain<br>3. Input nama yang sudah ada<br>4. Klik Simpan | Nama: Air Mineral (sudah ada)<br>Kategori: Minuman<br>Harga: 3000 | Muncul notifikasi "Nama produk sudah ada" |
| 23 | F08 - Tambah Produk Lain | Tambah produk dengan kategori kosong | 1. Login sebagai user<br>2. Buka form tambah produk lain<br>3. Kosongkan kategori<br>4. Klik Simpan | Nama: Produk Baru<br>Kategori: (kosong)<br>Harga: 5000 | Muncul notifikasi "Kategori wajib dipilih" |
| 24 | F08 - Tambah Produk Lain | Tambah produk dengan harga berupa text | 1. Login sebagai user<br>2. Buka form tambah produk lain<br>3. Input harga berupa huruf<br>4. Klik Simpan | Nama: Produk Test<br>Kategori: Lain-lain<br>Harga: "lima ribu" | Muncul notifikasi "Harga harus berupa angka" |
| 25 | F09 - Lihat Transaksi | Filter transaksi dengan tanggal kosong | 1. Login sebagai user<br>2. Buka daftar transaksi<br>3. Kosongkan filter tanggal<br>4. Klik Filter | Tanggal Mulai: (kosong)<br>Tanggal Akhir: (kosong) | Tampil semua transaksi tanpa filter |
| 26 | F09 - Lihat Transaksi | Search transaksi dengan keyword tidak ada | 1. Login sebagai user<br>2. Buka daftar transaksi<br>3. Input keyword yang tidak ada<br>4. Klik Search | Keyword: "xyz999notfound" | Tampil pesan "Tidak ada transaksi yang ditemukan" |
| 27 | F09 - Lihat Transaksi | Filter transaksi dengan range tanggal masa depan | 1. Login sebagai user<br>2. Buka daftar transaksi<br>3. Set tanggal masa depan<br>4. Klik Filter | Tanggal Mulai: 2026-01-01<br>Tanggal Akhir: 2026-12-31 | Tampil pesan "Tidak ada transaksi pada periode tersebut" |
| 28 | F10 - Membuat Transaksi | Buat transaksi tanpa pilih produk | 1. Login sebagai user<br>2. Buka form transaksi<br>3. Isi customer tanpa produk<br>4. Klik Simpan | Customer: John Doe<br>Produk: (tidak dipilih)<br>Total: 0 | Muncul notifikasi "Minimal pilih satu produk" |
| 29 | F10 - Membuat Transaksi | Buat transaksi dengan quantity melebihi stok | 1. Login sebagai user<br>2. Pilih produk dengan stok 5<br>3. Input quantity 10<br>4. Klik Simpan | Produk: Ubi Original (stok: 5)<br>Quantity: 10<br>Customer: Jane Doe | Muncul notifikasi "Stok tidak mencukupi (tersedia: 5)" |
| 30 | F10 - Membuat Transaksi | Buat transaksi dengan customer name kosong | 1. Login sebagai user<br>2. Pilih produk dan quantity<br>3. Kosongkan nama customer<br>4. Klik Simpan | Produk: Ubi Original<br>Quantity: 2<br>Customer: (kosong) | Muncul notifikasi "Nama customer wajib diisi" |
| 31 | F10 - Membuat Transaksi | Buat transaksi dengan quantity nol | 1. Login sebagai user<br>2. Pilih produk<br>3. Set quantity 0<br>4. Klik Simpan | Produk: Ubi Original<br>Quantity: 0<br>Customer: Test User | Muncul notifikasi "Quantity harus lebih dari 0" |
| 32 | F10 - Membuat Transaksi | Buat transaksi dengan quantity negatif | 1. Login sebagai user<br>2. Pilih produk<br>3. Input quantity negatif<br>4. Klik Simpan | Produk: Ubi Original<br>Quantity: -5<br>Customer: Test User | Muncul notifikasi "Quantity tidak boleh negatif" |
| 33 | Security | Akses halaman admin sebagai employee | 1. Login sebagai employee<br>2. Akses URL admin langsung<br>3. Amati response | User: employee<br>URL: /admin/users | Redirect ke dashboard dengan pesan "Akses ditolak" |
| 34 | Security | SQL Injection pada login | 1. Buka halaman login<br>2. Input SQL injection<br>3. Klik Login | Username: <EMAIL><br>Password: ' OR '1'='1 | Login gagal, muncul notifikasi "Username atau password salah" |
| 35 | Security | XSS pada form input | 1. Login sebagai user<br>2. Input script pada form<br>3. Simpan data | Input: `<script>alert('xss')</script>` | Script di-escape, tidak dieksekusi, data tersimpan aman |
| 36 | Performance | Load halaman dengan data besar | 1. Login sebagai user<br>2. Akses laporan dengan 1000+ data<br>3. Ukur waktu load | Data: 1000+ transaksi | Halaman load dalam waktu < 10 detik |
| 37 | Usability | Navigasi dengan breadcrumb | 1. Login sebagai user<br>2. Navigasi ke sub-menu<br>3. Klik breadcrumb | Path: Dashboard > Produk > Tambah Ubi Mentah | Berhasil kembali ke halaman sebelumnya |
| 38 | Usability | Form validation real-time | 1. Login sebagai user<br>2. Buka form input<br>3. Input data invalid<br>4. Pindah ke field lain | Email: format salah | Muncul pesan error real-time tanpa submit |
| 39 | Compatibility | Akses dari mobile browser | 1. Buka sistem dari mobile<br>2. Login sebagai user<br>3. Test navigasi | Device: Smartphone Android/iOS | Interface responsive, navigasi mudah digunakan |
| 40 | Compatibility | Akses dari browser lama | 1. Buka sistem dari IE/Safari lama<br>2. Login sebagai user<br>3. Test fungsionalitas | Browser: Internet Explorer 11 | Fungsionalitas dasar tetap berjalan atau ada pesan browser tidak didukung |

---

## 📊 **SUMMARY TEST CASES BLACK BOX**

| **Kategori** | **Jumlah Test Case** | **Fokus Testing** |
|--------------|---------------------|-------------------|
| **Input Validation** | 15 | Format, required fields, data types |
| **Business Logic** | 10 | Stok, harga, quantity, duplikasi |
| **Security** | 5 | Authentication, authorization, injection |
| **Error Handling** | 8 | Empty data, invalid input, edge cases |
| **Performance** | 1 | Load time dengan data besar |
| **Usability** | 2 | Navigation, real-time validation |
| **Compatibility** | 2 | Mobile, browser support |
| **TOTAL** | **40** | **Comprehensive coverage** |

---

## 🎯 **BOUNDARY VALUE ANALYSIS**

| **Field** | **Valid Range** | **Boundary Values** | **Invalid Values** |
|-----------|-----------------|--------------------|--------------------|
| **Quantity** | 1 - 9999 | 0, 1, 9999, 10000 | -1, 0, 10001 |
| **Harga** | 1 - 999999999 | 0, 1, 999999999 | -1, 0, 1000000000 |
| **Password Length** | 8 - 50 | 7, 8, 50, 51 | 1-7, 51+ |
| **Email Length** | 5 - 100 | 4, 5, 100, 101 | 1-4, 101+ |
| **Nama Produk** | 1 - 100 | 0, 1, 100, 101 | empty, 101+ |

---

## ✅ **KRITERIA PASS/FAIL BLACK BOX**

### **PASS Criteria:**
- ✅ Semua input validation berfungsi dengan benar
- ✅ Error messages informatif dan user-friendly
- ✅ Business logic sesuai requirement
- ✅ Security measures efektif
- ✅ Performance dalam batas acceptable

### **FAIL Criteria:**
- ❌ Invalid input diterima sistem
- ❌ Error messages tidak jelas atau tidak muncul
- ❌ Business logic tidak sesuai requirement
- ❌ Security vulnerability ditemukan
- ❌ Performance di bawah standar

---

## 📋 **EXECUTION NOTES**

### **Test Environment:**
- Browser: Chrome, Firefox, Safari, Edge
- OS: Windows, macOS, Linux
- Mobile: Android, iOS
- Database: MySQL dengan test data

### **Test Data Requirements:**
- User admin dan employee
- Produk dengan berbagai kategori
- Transaksi sample
- Data boundary values

### **Reporting:**
- Screenshot untuk setiap failed test
- Performance metrics
- Browser compatibility matrix
- Security test results

---

**📅 Document Version**: 1.0  
**👨‍💻 Created By**: QA Team  
**📧 Contact**: <EMAIL>  
**🔄 Last Updated**: 27 Juli 2025
