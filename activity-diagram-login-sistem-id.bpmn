<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_LoginSistem" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.0.0">
  <bpmn:collaboration id="Collaboration_LoginSistem">
    <bpmn:participant id="Participant_Pengguna" name="Pengguna" processRef="Process_Pengguna" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_MasukkanKredensial" targetRef="Task_ValidasiLogin" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanDashboard" targetRef="Task_GunakanSistem" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_TampilkanKesalahan" targetRef="Task_CobaLagi" />
  </bpmn:collaboration>

  <bpmn:process id="Process_Pengguna" name="Proses Pengguna" isExecutable="false">
    <bpmn:startEvent id="StartEvent_Login" name="Mulai Login">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_MasukkanKredensial" name="Masukkan username dan password">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_GunakanSistem" name="Gunakan sistem">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_CobaLagi" name="Coba login lagi">
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_Login" name="Selesai">
      <bpmn:incoming>Flow_4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_Login" targetRef="Task_MasukkanKredensial" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_MasukkanKredensial" targetRef="Task_GunakanSistem" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_GunakanSistem" targetRef="EndEvent_Login" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_CobaLagi" targetRef="Task_MasukkanKredensial" />
  </bpmn:process>

  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_ValidasiLogin" name="Validasi kredensial">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_Valid" name="Kredensial valid?">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_TampilkanDashboard" name="Tampilkan dashboard">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanKesalahan" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ValidasiLogin" targetRef="Gateway_Valid" />
    <bpmn:sequenceFlow id="Flow_S2" name="Ya" sourceRef="Gateway_Valid" targetRef="Task_TampilkanDashboard" />
    <bpmn:sequenceFlow id="Flow_S3" name="Tidak" sourceRef="Gateway_Valid" targetRef="Task_TampilkanKesalahan" />
  </bpmn:process>

  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_LoginSistem">
      <bpmndi:BPMNShape id="Participant_Pengguna_di" bpmnElement="Participant_Pengguna" isHorizontal="true">
        <dc:Bounds x="160" y="80" width="1000" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_Sistem_di" bpmnElement="Participant_Sistem" isHorizontal="true">
        <dc:Bounds x="160" y="320" width="1000" height="300" />
      </bpmndi:BPMNShape>

      <!-- User Process Elements -->
      <bpmndi:BPMNShape id="StartEvent_Login_di" bpmnElement="StartEvent_Login">
        <dc:Bounds x="212" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="195" y="205" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape id="Task_MasukkanKredensial_di" bpmnElement="Task_MasukkanKredensial">
        <dc:Bounds x="300" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_GunakanSistem_di" bpmnElement="Task_GunakanSistem">
        <dc:Bounds x="600" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_CobaLagi_di" bpmnElement="Task_CobaLagi">
        <dc:Bounds x="450" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_Login_di" bpmnElement="EndEvent_Login">
        <dc:Bounds x="752" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="750" y="205" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- System Process Elements -->
      <bpmndi:BPMNShape id="Task_TampilkanFormLogin_di" bpmnElement="Task_TampilkanFormLogin">
        <dc:Bounds x="300" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_ValidasiLogin_di" bpmnElement="Task_ValidasiLogin">
        <dc:Bounds x="300" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_Valid_di" bpmnElement="Gateway_Valid" isMarkerVisible="true">
        <dc:Bounds x="445" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="425" y="452" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape id="Task_TampilkanDashboard_di" bpmnElement="Task_TampilkanDashboard">
        <dc:Bounds x="600" y="340" width="100" height="80" />
      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape id="Task_TampilkanKesalahan_di" bpmnElement="Task_TampilkanKesalahan">
        <dc:Bounds x="600" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- Sequence Flows -->
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="248" y="180" />
        <di:waypoint x="300" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="400" y="180" />
        <di:waypoint x="600" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_4_di" bpmnElement="Flow_4">
        <di:waypoint x="700" y="180" />
        <di:waypoint x="752" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_6_di" bpmnElement="Flow_6">
        <di:waypoint x="500" y="240" />
        <di:waypoint x="500" y="200" />
        <di:waypoint x="350" y="200" />
        <di:waypoint x="350" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_7_di" bpmnElement="Flow_7">
        <di:waypoint x="900" y="480" />
        <di:waypoint x="900" y="300" />
        <di:waypoint x="700" y="300" />
        <di:waypoint x="700" y="220" />
      </bpmndi:BPMNEdge>

      <!-- System Sequence Flows -->
      <bpmndi:BPMNEdge id="Flow_S1_di" bpmnElement="Flow_S1">
        <di:waypoint x="400" y="420" />
        <di:waypoint x="445" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_S2_di" bpmnElement="Flow_S2">
        <di:waypoint x="470" y="395" />
        <di:waypoint x="470" y="380" />
        <di:waypoint x="600" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="478" y="385" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_S3_di" bpmnElement="Flow_S3">
        <di:waypoint x="470" y="445" />
        <di:waypoint x="470" y="520" />
        <di:waypoint x="600" y="520" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="472" y="480" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <!-- Message Flows -->
      <bpmndi:BPMNEdge id="MessageFlow_1_di" bpmnElement="MessageFlow_1">
        <di:waypoint x="350" y="220" />
        <di:waypoint x="350" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="MessageFlow_2_di" bpmnElement="MessageFlow_2">
        <di:waypoint x="650" y="340" />
        <di:waypoint x="650" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="MessageFlow_3_di" bpmnElement="MessageFlow_3">
        <di:waypoint x="650" y="480" />
        <di:waypoint x="650" y="350" />
        <di:waypoint x="500" y="350" />
        <di:waypoint x="500" y="320" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
