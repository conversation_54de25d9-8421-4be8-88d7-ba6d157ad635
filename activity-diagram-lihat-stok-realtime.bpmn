<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_ViewRealtimeStock" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_ViewRealtimeStock">
    <bpmn:participant id="Participant_Cashier" name="Cashier" processRef="Process_Cashier" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_OpenStockMenu" targetRef="Task_GetStockData" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowStock" targetRef="Task_ViewStock" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_RefreshStock" targetRef="Task_RefreshData" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Cashier" name="Cashier Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_ViewStock" name="Start View Stock">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenStockMenu" name="Open view stock menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ViewStock" name="View stock information">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_RefreshStock" name="Need refresh?">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_RefreshData" name="Refresh stock data">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_ViewStock" name="End">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_ViewStock" targetRef="Task_OpenStockMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenStockMenu" targetRef="Task_ViewStock" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_ViewStock" targetRef="Gateway_RefreshStock" />
    <bpmn:sequenceFlow id="Flow_4" name="Yes" sourceRef="Gateway_RefreshStock" targetRef="Task_RefreshData" />
    <bpmn:sequenceFlow id="Flow_5" name="No" sourceRef="Gateway_RefreshStock" targetRef="EndEvent_ViewStock" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_RefreshData" targetRef="EndEvent_ViewStock" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_GetStockData" name="Get current stock data">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatData" name="Format data for display">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowStock" name="Show realtime stock data">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_RefreshStock" name="Refresh stock data">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_UpdateDisplay" name="Update stock display">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_GetStockData" targetRef="Task_FormatData" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_FormatData" targetRef="Task_ShowStock" />
    <bpmn:sequenceFlow id="Flow_S3" sourceRef="Task_ShowStock" targetRef="Task_RefreshStock" />
    <bpmn:sequenceFlow id="Flow_S4" sourceRef="Task_RefreshStock" targetRef="Task_UpdateDisplay" />
  </bpmn:process>
</bpmn:definitions>
