<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_LihatStokRealtime" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_LihatStokRealtime">
    <bpmn:participant id="Participant_Kasir" name="Kasir" processRef="Process_Kasir" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_BukaMenuStok" targetRef="Task_AmbilDataStok" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanStok" targetRef="Task_LihatStok" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_RefreshStok" targetRef="Task_RefreshData" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Kasir" name="Proses Kasir" isExecutable="false">
    <bpmn:startEvent id="StartEvent_LihatStok" name="Mulai Lihat Stok">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_BukaMenuStok" name="Buka menu lihat stok">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_LihatStok" name="Lihat informasi stok">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_RefreshStok" name="Perlu refresh?">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_RefreshData" name="Refresh data stok">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_LihatStok" name="Selesai">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_LihatStok" targetRef="Task_BukaMenuStok" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_BukaMenuStok" targetRef="Task_LihatStok" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_LihatStok" targetRef="Gateway_RefreshStok" />
    <bpmn:sequenceFlow id="Flow_4" name="Ya" sourceRef="Gateway_RefreshStok" targetRef="Task_RefreshData" />
    <bpmn:sequenceFlow id="Flow_5" name="Tidak" sourceRef="Gateway_RefreshStok" targetRef="EndEvent_LihatStok" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_RefreshData" targetRef="EndEvent_LihatStok" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_AmbilDataStok" name="Ambil data stok terkini">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatData" name="Format data untuk tampilan">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanStok" name="Tampilkan data stok realtime">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_RefreshStok" name="Refresh data stok">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_UpdateTampilan" name="Update tampilan stok">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_AmbilDataStok" targetRef="Task_FormatData" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_FormatData" targetRef="Task_TampilkanStok" />
    <bpmn:sequenceFlow id="Flow_S3" sourceRef="Task_TampilkanStok" targetRef="Task_RefreshStok" />
    <bpmn:sequenceFlow id="Flow_S4" sourceRef="Task_RefreshStok" targetRef="Task_UpdateTampilan" />
  </bpmn:process>
</bpmn:definitions>
