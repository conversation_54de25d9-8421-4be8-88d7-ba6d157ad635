@startuml Sequence_Logout_Sistem
title Sequence Diagram - Logout Sistem (Include Audit Trail)

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor User
participant ":DashboardPage" as DP
participant ":AuthController" as AC
participant ":AuditController" as AuC
participant ":Database" as DB

User -> DP: 1. clickLogout()
activate DP

DP -> AC: 2. initiateLogout()
activate AC

AC -> AuC: 2.1. logLogoutActivity(userId)
activate AuC
note right: Include relationship

AuC -> DB: 2.2. saveAuditLog(logoutEvent)
activate DB
DB --> AuC: 2.3. auditSaved()
deactivate DB

AuC --> AC: 2.4. auditCompleted()
deactivate AuC

AC -> DB: 2.5. invalidateSession(sessionId)
activate DB
DB --> AC: 2.6. sessionInvalidated()
deactivate DB

AC -> DB: 2.7. clearUserTokens(userId)
activate DB
DB --> AC: 2.8. tokensCleared()
deactivate DB

AC --> DP: 2.9. logoutSuccess()
DP --> User: 2.10. redirectToLoginPage()

deactivate AC
deactivate DP

@enduml
