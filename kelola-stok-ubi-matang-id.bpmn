<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_stock_manage" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_stock_manage">
    <bpmn:participant id="Participant_cashier" name="Kasir" processRef="Process_cashier" />
    <bpmn:participant id="Participant_system" name="Sistem" processRef="Process_system" />
    <bpmn:messageFlow id="Flow_stock_1" sourceRef="Activity_enter_qty" targetRef="Activity_validate_input" />
    <bpmn:messageFlow id="Flow_stock_2" sourceRef="Activity_show_confirm" targetRef="Activity_complete_manage" />
  </bpmn:collaboration>
  <bpmn:process id="Process_cashier" isExecutable="true">
    <bpmn:startEvent id="StartEvent_stock" name="Mulai Kelola Stok">
      <bpmn:outgoing>Flow_start_stock</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Activity_open_stock" name="Buka menu kelola stok">
      <bpmn:incoming>Flow_start_stock</bpmn:incoming>
      <bpmn:outgoing>Flow_open_select</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_select_op" name="Pilih operasi (tambah/kurang)">
      <bpmn:incoming>Flow_open_select</bpmn:incoming>
      <bpmn:outgoing>Flow_select_enter</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_enter_qty" name="Masukkan jumlah ubi">
      <bpmn:incoming>Flow_select_enter</bpmn:incoming>
      <bpmn:incoming>Flow_retry</bpmn:incoming>
      <bpmn:outgoing>Flow_enter_complete</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_complete_manage" name="Selesai kelola stok">
      <bpmn:incoming>Flow_enter_complete</bpmn:incoming>
      <bpmn:outgoing>Flow_complete_end</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_try_again" name="Coba lagi dengan input yang valid">
      <bpmn:incoming>Flow_error_retry</bpmn:incoming>
      <bpmn:outgoing>Flow_retry</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="EndEvent_stock" name="Selesai">
      <bpmn:incoming>Flow_complete_end</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_start_stock" sourceRef="StartEvent_stock" targetRef="Activity_open_stock" />
    <bpmn:sequenceFlow id="Flow_open_select" sourceRef="Activity_open_stock" targetRef="Activity_select_op" />
    <bpmn:sequenceFlow id="Flow_select_enter" sourceRef="Activity_select_op" targetRef="Activity_enter_qty" />
    <bpmn:sequenceFlow id="Flow_enter_complete" sourceRef="Activity_enter_qty" targetRef="Activity_complete_manage" />
    <bpmn:sequenceFlow id="Flow_complete_end" sourceRef="Activity_complete_manage" targetRef="EndEvent_stock" />
    <bpmn:sequenceFlow id="Flow_retry" sourceRef="Activity_try_again" targetRef="Activity_enter_qty" />
    <bpmn:sequenceFlow id="Flow_error_retry" sourceRef="Activity_try_again" targetRef="Activity_enter_qty" />
  </bpmn:process>
  <bpmn:process id="Process_system" isExecutable="false">
    <bpmn:task id="Activity_show_form" name="Tampilkan form kelola stok">
      <bpmn:outgoing>Flow_form_validate</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_validate_input" name="Validasi input">
      <bpmn:incoming>Flow_form_validate</bpmn:incoming>
      <bpmn:outgoing>Flow_validate_gateway</bpmn:outgoing>
    </bpmn:task>
    <bpmn:exclusiveGateway id="Gateway_valid" name="Input valid?">
      <bpmn:incoming>Flow_validate_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_valid_yes</bpmn:outgoing>
      <bpmn:outgoing>Flow_valid_no</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:task id="Activity_update_stock" name="Update stok ubi matang">
      <bpmn:incoming>Flow_valid_yes</bpmn:incoming>
      <bpmn:outgoing>Flow_update_audit</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_log_audit" name="Catat log audit (Include)">
      <bpmn:incoming>Flow_update_audit</bpmn:incoming>
      <bpmn:outgoing>Flow_audit_confirm</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_show_confirm" name="Tampilkan konfirmasi berhasil">
      <bpmn:incoming>Flow_audit_confirm</bpmn:incoming>
    </bpmn:task>
    <bpmn:task id="Activity_show_error" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_valid_no</bpmn:incoming>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_form_validate" sourceRef="Activity_show_form" targetRef="Activity_validate_input" />
    <bpmn:sequenceFlow id="Flow_validate_gateway" sourceRef="Activity_validate_input" targetRef="Gateway_valid" />
    <bpmn:sequenceFlow id="Flow_valid_yes" name="Ya" sourceRef="Gateway_valid" targetRef="Activity_update_stock" />
    <bpmn:sequenceFlow id="Flow_valid_no" name="Tidak" sourceRef="Gateway_valid" targetRef="Activity_show_error" />
    <bpmn:sequenceFlow id="Flow_update_audit" sourceRef="Activity_update_stock" targetRef="Activity_log_audit" />
    <bpmn:sequenceFlow id="Flow_audit_confirm" sourceRef="Activity_log_audit" targetRef="Activity_show_confirm" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_stock">
    <bpmndi:BPMNPlane id="BPMNPlane_stock" bpmnElement="Collaboration_stock_manage">
      <bpmndi:BPMNShape id="Participant_cashier_di" bpmnElement="Participant_cashier" isHorizontal="true">
        <dc:Bounds x="129" y="80" width="1051" height="250" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_complete_end_di" bpmnElement="Flow_complete_end">
        <di:waypoint x="1020" y="177" />
        <di:waypoint x="1102" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_enter_complete_di" bpmnElement="Flow_enter_complete">
        <di:waypoint x="720" y="177" />
        <di:waypoint x="920" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_select_enter_di" bpmnElement="Flow_select_enter">
        <di:waypoint x="520" y="177" />
        <di:waypoint x="570" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_open_select_di" bpmnElement="Flow_open_select">
        <di:waypoint x="370" y="177" />
        <di:waypoint x="420" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_start_stock_di" bpmnElement="Flow_start_stock">
        <di:waypoint x="215" y="177" />
        <di:waypoint x="270" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_retry_di" bpmnElement="Flow_retry">
        <di:waypoint x="770" y="177" />
        <di:waypoint x="720" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_stock_di" bpmnElement="StartEvent_stock">
        <dc:Bounds x="179" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="155" y="202" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_open_stock_di" bpmnElement="Activity_open_stock">
        <dc:Bounds x="270" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_select_op_di" bpmnElement="Activity_select_op">
        <dc:Bounds x="420" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_enter_qty_di" bpmnElement="Activity_enter_qty">
        <dc:Bounds x="570" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_complete_manage_di" bpmnElement="Activity_complete_manage">
        <dc:Bounds x="920" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_try_again_di" bpmnElement="Activity_try_again">
        <dc:Bounds x="770" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_stock_di" bpmnElement="EndEvent_stock">
        <dc:Bounds x="1102" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1100" y="202" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_system_di" bpmnElement="Participant_system" isHorizontal="true">
        <dc:Bounds x="129" y="380" width="1051" height="280" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_audit_confirm_di" bpmnElement="Flow_audit_confirm">
        <di:waypoint x="870" y="520" />
        <di:waypoint x="920" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_update_audit_di" bpmnElement="Flow_update_audit">
        <di:waypoint x="720" y="520" />
        <di:waypoint x="770" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_valid_no_di" bpmnElement="Flow_valid_no">
        <di:waypoint x="495" y="545" />
        <di:waypoint x="495" y="600" />
        <di:waypoint x="620" y="600" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="497" y="570" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_valid_yes_di" bpmnElement="Flow_valid_yes">
        <di:waypoint x="520" y="520" />
        <di:waypoint x="620" y="520" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="565" y="502" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_validate_gateway_di" bpmnElement="Flow_validate_gateway">
        <di:waypoint x="420" y="520" />
        <di:waypoint x="470" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_form_validate_di" bpmnElement="Flow_form_validate">
        <di:waypoint x="270" y="520" />
        <di:waypoint x="320" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_show_form_di" bpmnElement="Activity_show_form">
        <dc:Bounds x="170" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_validate_input_di" bpmnElement="Activity_validate_input">
        <dc:Bounds x="320" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_valid_di" bpmnElement="Gateway_valid" isMarkerVisible="true">
        <dc:Bounds x="470" y="495" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="467" y="552" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_update_stock_di" bpmnElement="Activity_update_stock">
        <dc:Bounds x="620" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_log_audit_di" bpmnElement="Activity_log_audit">
        <dc:Bounds x="770" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_show_confirm_di" bpmnElement="Activity_show_confirm">
        <dc:Bounds x="920" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_show_error_di" bpmnElement="Activity_show_error">
        <dc:Bounds x="620" y="560" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_stock_1_di" bpmnElement="Flow_stock_1">
        <di:waypoint x="620" y="217" />
        <di:waypoint x="620" y="350" />
        <di:waypoint x="370" y="350" />
        <di:waypoint x="370" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_stock_2_di" bpmnElement="Flow_stock_2">
        <di:waypoint x="970" y="480" />
        <di:waypoint x="970" y="217" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
