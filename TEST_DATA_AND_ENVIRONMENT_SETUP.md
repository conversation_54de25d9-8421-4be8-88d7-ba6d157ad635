# 🛠️ TEST DATA & ENVIRONMENT SETUP
# SISTEM INFORMASI MANAJEMEN UBI BAKAR CILEMBU

## 📋 **INFORMASI DOKUMEN**

| **Atribut** | **Detail** |
|-------------|------------|
| **Nama Sistem** | Sistem Informasi Manajemen Ubi Bakar Cilembu |
| **Purpose** | Test Environment Setup & Test Data Preparation |
| **Tanggal Dibuat** | 27 Juli 2025 |
| **Environment** | Testing Environment |
| **Database** | MySQL Test Database |

---

## 🎯 **TUJUAN DOKUMEN**

Dokumen ini berisi:
1. ✅ Panduan setup environment testing
2. ✅ Test data yang diperlukan untuk testing
3. ✅ Konfigurasi sistem untuk testing
4. ✅ Backup dan restore procedures
5. ✅ Troubleshooting common issues

---

## 🖥️ **ENVIRONMENT SETUP**

### **📋 System Requirements**

| **Component** | **Requirement** | **Recommended** |
|---------------|-----------------|-----------------|
| **OS** | Windows 10/11, macOS, Linux | Windows 11 |
| **PHP** | >= 8.2 | PHP 8.3 |
| **MySQL** | >= 8.0 | MySQL 8.0.35 |
| **Composer** | Latest | 2.6+ |
| **Node.js** | >= 18.0 | Node.js 20+ |
| **NPM** | >= 9.0 | NPM 10+ |
| **Memory** | 4GB RAM | 8GB RAM |
| **Storage** | 2GB free space | 5GB free space |

### **🔧 Installation Steps**

#### **1. Clone Repository**
```bash
git clone https://github.com/username/ubi-bakar-cilembu.git
cd ubi-bakar-cilembu
```

#### **2. Install Dependencies**
```bash
# Install PHP dependencies
composer install

# Install JavaScript dependencies
npm install
```

#### **3. Environment Configuration**
```bash
# Copy environment file
cp .env.example .env.testing

# Generate application key
php artisan key:generate --env=testing
```

#### **4. Database Setup**
```bash
# Create test database
mysql -u root -p
CREATE DATABASE ubi_bakar_test;
CREATE DATABASE ubi_bakar_test_backup;
exit;

# Run migrations
php artisan migrate --env=testing

# Seed test data
php artisan db:seed --env=testing
```

#### **5. Build Assets**
```bash
# Compile assets for testing
npm run build
```

#### **6. Start Test Server**
```bash
# Start Laravel development server
php artisan serve --env=testing --port=8001
```

---

## 🗄️ **DATABASE CONFIGURATION**

### **📝 .env.testing Configuration**
```env
APP_NAME="Ubi Bakar Cilembu - Testing"
APP_ENV=testing
APP_KEY=base64:GENERATED_KEY_HERE
APP_DEBUG=true
APP_URL=http://localhost:8001

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ubi_bakar_test
DB_USERNAME=root
DB_PASSWORD=your_password

# Midtrans Configuration (Sandbox)
MIDTRANS_SERVER_KEY=your_sandbox_server_key
MIDTRANS_CLIENT_KEY=your_sandbox_client_key
MIDTRANS_IS_PRODUCTION=false
MIDTRANS_IS_SANITIZED=true
MIDTRANS_IS_3DS=true

# Mail Configuration (Testing)
MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Ubi Bakar Test"

# Cache & Session (Testing)
CACHE_DRIVER=array
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120
```

### **🔄 Database Backup & Restore**

#### **Create Backup**
```bash
# Create database backup before testing
mysqldump -u root -p ubi_bakar_test > backup_before_testing.sql

# Or use Laravel command
php artisan backup:run --only-db --env=testing
```

#### **Restore Database**
```bash
# Restore from backup
mysql -u root -p ubi_bakar_test < backup_before_testing.sql

# Or reset and reseed
php artisan migrate:fresh --seed --env=testing
```

---

## 👥 **TEST USERS DATA**

### **🔐 Admin Users**

| **Name** | **Email** | **Password** | **Role** | **Purpose** |
|----------|-----------|--------------|----------|-------------|
| Super Admin | <EMAIL> | admin123 | admin | Full system access |
| Admin Toko | <EMAIL> | admin456 | admin | Store admin |
| Admin Test | <EMAIL> | testadmin | admin | Testing purposes |

### **👤 Employee Users**

| **Name** | **Email** | **Password** | **Role** | **Purpose** |
|----------|-----------|--------------|----------|-------------|
| Karyawan 1 | <EMAIL> | karyawan123 | employee | POS operations |
| Karyawan 2 | <EMAIL> | karyawan456 | employee | Inventory management |
| Test Employee | <EMAIL> | testemployee | employee | Testing purposes |

### **🔧 User Creation Script**
```sql
-- Insert test users
INSERT INTO users (name, email, password, role, created_at, updated_at) VALUES
('Super Admin', '<EMAIL>', '$2y$12$hash_for_admin123', 'admin', NOW(), NOW()),
('Admin Toko', '<EMAIL>', '$2y$12$hash_for_admin456', 'admin', NOW(), NOW()),
('Karyawan 1', '<EMAIL>', '$2y$12$hash_for_karyawan123', 'employee', NOW(), NOW()),
('Karyawan 2', '<EMAIL>', '$2y$12$hash_for_karyawan456', 'employee', NOW(), NOW()),
('Test Employee', '<EMAIL>', '$2y$12$hash_for_testemployee', 'employee', NOW(), NOW());
```

---

## 📦 **TEST INVENTORY DATA**

### **🥔 Raw Inventory (Ubi Mentah)**

| **Batch Number** | **Name** | **Supplier** | **Quantity (kg)** | **Cost/kg** | **Quality** | **Expiry Date** |
|------------------|----------|--------------|-------------------|-------------|-------------|-----------------|
| RAW-001 | Ubi Cilembu Premium | Petani Cilembu | 100 | 15,000 | A | +30 days |
| RAW-002 | Ubi Cilembu Standard | Petani Bandung | 150 | 12,000 | B | +25 days |
| RAW-003 | Ubi Cilembu Organik | Petani Organik | 80 | 18,000 | A | +35 days |
| RAW-004 | Ubi Cilembu Biasa | Supplier Lokal | 200 | 10,000 | C | +20 days |
| RAW-005 | Ubi Cilembu Super | Premium Supplier | 50 | 20,000 | A | +40 days |

### **🍠 Processed Inventory (Ubi Bakar)**

| **Batch Number** | **Name** | **Product Type** | **Stock** | **Cost/Unit** | **Selling Price** | **Expiry Date** |
|------------------|----------|------------------|-----------|---------------|-------------------|-----------------|
| UBI-001 | Ubi Bakar Original | Original | 200 | 5,000 | 8,000 | +7 days |
| UBI-002 | Ubi Bakar Premium | Premium | 150 | 6,000 | 10,000 | +7 days |
| UBI-003 | Ubi Bakar Madu | Special | 100 | 7,000 | 12,000 | +5 days |
| UBI-004 | Ubi Bakar Keju | Special | 80 | 8,000 | 15,000 | +5 days |
| UBI-005 | Ubi Bakar Coklat | Special | 60 | 9,000 | 18,000 | +5 days |

### **🥤 Other Products**

| **Name** | **Category** | **Stock** | **Cost** | **Selling Price** | **Supplier** |
|----------|--------------|-----------|----------|-------------------|--------------|
| Air Mineral | Minuman | 100 | 2,000 | 3,000 | Supplier Minuman |
| Teh Botol | Minuman | 80 | 3,000 | 5,000 | Supplier Minuman |
| Kopi Sachet | Minuman | 50 | 1,500 | 2,500 | Supplier Kopi |
| Kerupuk Ubi | Snack | 40 | 5,000 | 8,000 | Supplier Snack |
| Plastik Kemasan | Packaging | 200 | 500 | 1,000 | Supplier Kemasan |

---

## 💳 **PAYMENT GATEWAY SETUP**

### **🔧 Midtrans Sandbox Configuration**

#### **Test Credentials**
```env
MIDTRANS_SERVER_KEY=SB-Mid-server-your_sandbox_server_key
MIDTRANS_CLIENT_KEY=SB-Mid-client-your_sandbox_client_key
MIDTRANS_IS_PRODUCTION=false
```

#### **Test Credit Cards**

| **Card Number** | **CVV** | **Exp Date** | **Result** |
|-----------------|---------|--------------|------------|
| 4811 1111 1111 1114 | 123 | 01/25 | Success |
| 4911 1111 1111 1113 | 123 | 01/25 | Challenge by FDS |
| 4411 1111 1111 1118 | 123 | 01/25 | Insufficient funds |
| 4511 1111 1111 1117 | 123 | 01/25 | Invalid card |

#### **Test Bank Accounts**

| **Bank** | **VA Number** | **Result** |
|----------|---------------|------------|
| BCA | *********** | Success |
| BNI | *********** | Success |
| BRI | *********** | Success |
| Mandiri | *********** | Success |

#### **Test E-Wallets**

| **E-Wallet** | **Phone** | **Result** |
|--------------|-----------|------------|
| GoPay | ************ | Success |
| OVO | ************ | Success |
| DANA | ************ | Success |
| ShopeePay | ************ | Success |

---

## 🧪 **TEST SCENARIOS DATA**

### **📊 Transaction Test Data**

#### **Scenario 1: Small Transaction**
```json
{
  "customer_name": "John Doe",
  "customer_phone": "************",
  "items": [
    {"product": "Ubi Bakar Original", "quantity": 2, "price": 8000},
    {"product": "Air Mineral", "quantity": 1, "price": 3000}
  ],
  "payment_method": "cash",
  "amount_paid": 20000,
  "expected_total": 19000,
  "expected_change": 1000
}
```

#### **Scenario 2: Large Transaction**
```json
{
  "customer_name": "Jane Smith",
  "customer_phone": "081234567891",
  "items": [
    {"product": "Ubi Bakar Premium", "quantity": 10, "price": 10000},
    {"product": "Ubi Bakar Madu", "quantity": 5, "price": 12000},
    {"product": "Teh Botol", "quantity": 3, "price": 5000}
  ],
  "payment_method": "digital",
  "expected_total": 175000
}
```

#### **Scenario 3: Mixed Products**
```json
{
  "customer_name": "Bob Wilson",
  "customer_phone": "081234567892",
  "items": [
    {"product": "Ubi Bakar Original", "quantity": 3, "price": 8000},
    {"product": "Ubi Bakar Keju", "quantity": 2, "price": 15000},
    {"product": "Kerupuk Ubi", "quantity": 1, "price": 8000},
    {"product": "Air Mineral", "quantity": 2, "price": 3000}
  ],
  "payment_method": "cash",
  "amount_paid": 100000,
  "expected_total": 68000,
  "expected_change": 32000
}
```

### **🏭 Production Test Data**

#### **Production Scenario 1**
```json
{
  "raw_material": "RAW-001",
  "quantity_to_process": 50,
  "expected_output": 200,
  "product_type": "Original",
  "production_date": "2025-07-27",
  "expected_cost_per_unit": 5000
}
```

#### **Production Scenario 2**
```json
{
  "raw_material": "RAW-002",
  "quantity_to_process": 30,
  "expected_output": 100,
  "product_type": "Premium",
  "production_date": "2025-07-27",
  "expected_cost_per_unit": 6000
}
```

### **🚚 Distribution Test Data**

#### **Distribution Scenario 1**
```json
{
  "market_name": "Pasar Baru Bandung",
  "distribution_date": "2025-07-28",
  "products": [
    {"product_id": 1, "quantity": 50, "price": 7500},
    {"product_id": 2, "quantity": 30, "price": 9500}
  ],
  "notes": "Distribusi rutin ke Pasar Baru"
}
```

#### **Distribution Scenario 2**
```json
{
  "market_name": "Pasar Cicadas",
  "distribution_date": "2025-07-28",
  "products": [
    {"product_id": 3, "quantity": 25, "price": 11000},
    {"product_id": 4, "quantity": 20, "price": 14000}
  ],
  "notes": "Distribusi produk premium"
}
```

---

## 🔧 **TESTING TOOLS & UTILITIES**

### **📊 Database Seeder Commands**
```bash
# Seed all test data
php artisan db:seed --env=testing

# Seed specific seeders
php artisan db:seed --class=UserSeeder --env=testing
php artisan db:seed --class=RawInventorySeeder --env=testing
php artisan db:seed --class=ProcessedInventorySeeder --env=testing
php artisan db:seed --class=OtherProductSeeder --env=testing
```

### **🧹 Database Reset Commands**
```bash
# Fresh migration with seeding
php artisan migrate:fresh --seed --env=testing

# Reset specific tables
php artisan migrate:reset --env=testing
php artisan migrate --env=testing
```

### **🔍 Debug Commands**
```bash
# Check routes
php artisan route:list

# Check database connection
php artisan tinker
DB::connection()->getPdo();

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

---

## 🚨 **TROUBLESHOOTING**

### **❌ Common Issues & Solutions**

#### **Database Connection Error**
```bash
# Check MySQL service
sudo systemctl status mysql

# Check database exists
mysql -u root -p
SHOW DATABASES;

# Check .env configuration
cat .env.testing | grep DB_
```

#### **Permission Issues**
```bash
# Fix storage permissions
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/

# Fix ownership (Linux/Mac)
sudo chown -R www-data:www-data storage/
sudo chown -R www-data:www-data bootstrap/cache/
```

#### **Composer Issues**
```bash
# Update composer
composer self-update

# Clear composer cache
composer clear-cache

# Reinstall dependencies
rm -rf vendor/
composer install
```

#### **NPM Issues**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules/
npm install

# Build assets
npm run build
```

#### **Midtrans Integration Issues**
```bash
# Check Midtrans configuration
php artisan tinker
config('midtrans.server_key');
config('midtrans.client_key');

# Test Midtrans connection
curl -X GET https://api.sandbox.midtrans.com/v2/ping \
  -H "Authorization: Basic $(echo -n 'your_server_key:' | base64)"
```

---

## 📋 **PRE-TESTING CHECKLIST**

### **✅ Environment Setup**
- [ ] PHP 8.2+ installed
- [ ] MySQL 8.0+ running
- [ ] Composer dependencies installed
- [ ] NPM dependencies installed
- [ ] .env.testing configured
- [ ] Database created and migrated
- [ ] Test data seeded
- [ ] Assets compiled
- [ ] Server running on port 8001

### **✅ Test Data Verification**
- [ ] Admin users created
- [ ] Employee users created
- [ ] Raw inventory data loaded
- [ ] Processed inventory data loaded
- [ ] Other products data loaded
- [ ] Suppliers data loaded
- [ ] Test transactions created

### **✅ System Configuration**
- [ ] Midtrans sandbox configured
- [ ] Email testing configured
- [ ] File upload permissions set
- [ ] Cache cleared
- [ ] Debug mode enabled
- [ ] Logging configured

### **✅ Backup & Recovery**
- [ ] Database backup created
- [ ] Backup restoration tested
- [ ] Recovery procedures documented
- [ ] Rollback plan prepared

---

**📅 Document Version**: 1.0  
**👨‍💻 Created By**: QA Team  
**📧 Contact**: <EMAIL>  
**🔄 Last Updated**: 27 Juli 2025
