<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
  <bpmn2:collaboration id="Collaboration_1">
    <bpmn2:participant id="Participant_Kasir" name="Kasir" processRef="Process_Kasir" />
    <bpmn2:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn2:messageFlow id="MessageFlow_1" sourceRef="Task_MasukkanJumlah" targetRef="Task_ValidasiStok" />
    <bpmn2:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanTotal" targetRef="Task_TerimaUang" />
    <bpmn2:messageFlow id="MessageFlow_3" sourceRef="Task_TampilkanKembalian" targetRef="Task_SelesaiTransaksi" />
  </bpmn2:collaboration>
  
  <bpmn2:process id="Process_Kasir" isExecutable="false">
    <bpmn2:startEvent id="StartEvent_1" name="Mulai Transaksi">
      <bpmn2:outgoing>SequenceFlow_1</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:task id="Task_BukaMenuTransaksi" name="Buka menu transaksi">
      <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_MasukkanJumlah" name="Masukkan jumlah ubi">
      <bpmn2:incoming>SequenceFlow_2</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_3</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_TerimaUang" name="Terima uang dari pelanggan">
      <bpmn2:incoming>SequenceFlow_3</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_4</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_SelesaiTransaksi" name="Selesai transaksi">
      <bpmn2:incoming>SequenceFlow_4</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_5</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:endEvent id="EndEvent_1" name="Selesai">
      <bpmn2:incoming>SequenceFlow_5</bpmn2:incoming>
    </bpmn2:endEvent>
    <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="Task_BukaMenuTransaksi" />
    <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="Task_BukaMenuTransaksi" targetRef="Task_MasukkanJumlah" />
    <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="Task_MasukkanJumlah" targetRef="Task_TerimaUang" />
    <bpmn2:sequenceFlow id="SequenceFlow_4" sourceRef="Task_TerimaUang" targetRef="Task_SelesaiTransaksi" />
    <bpmn2:sequenceFlow id="SequenceFlow_5" sourceRef="Task_SelesaiTransaksi" targetRef="EndEvent_1" />
  </bpmn2:process>
  
  <bpmn2:process id="Process_Sistem" isExecutable="false">
    <bpmn2:task id="Task_ValidasiStok" name="Validasi stok tersedia">
      <bpmn2:outgoing>SequenceFlow_S1</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:exclusiveGateway id="Gateway_1" name="Stok cukup?">
      <bpmn2:incoming>SequenceFlow_S1</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S2</bpmn2:outgoing>
      <bpmn2:outgoing>SequenceFlow_S3</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:task id="Task_HitungTotal" name="Hitung total harga">
      <bpmn2:incoming>SequenceFlow_S2</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S4</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_TampilkanTotal" name="Tampilkan total harga">
      <bpmn2:incoming>SequenceFlow_S4</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S5</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_HitungKembalian" name="Hitung kembalian">
      <bpmn2:incoming>SequenceFlow_S5</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S6</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_UpdateStok" name="Update stok ubi">
      <bpmn2:incoming>SequenceFlow_S6</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S7</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_SimpanTransaksi" name="Simpan data transaksi">
      <bpmn2:incoming>SequenceFlow_S7</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S8</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_TampilkanKembalian" name="Tampilkan kembalian">
      <bpmn2:incoming>SequenceFlow_S8</bpmn2:incoming>
    </bpmn2:task>
    <bpmn2:task id="Task_TampilkanError" name="Tampilkan pesan stok tidak cukup">
      <bpmn2:incoming>SequenceFlow_S3</bpmn2:incoming>
    </bpmn2:task>
    <bpmn2:sequenceFlow id="SequenceFlow_S1" sourceRef="Task_ValidasiStok" targetRef="Gateway_1" />
    <bpmn2:sequenceFlow id="SequenceFlow_S2" name="Ya" sourceRef="Gateway_1" targetRef="Task_HitungTotal" />
    <bpmn2:sequenceFlow id="SequenceFlow_S3" name="Tidak" sourceRef="Gateway_1" targetRef="Task_TampilkanError" />
    <bpmn2:sequenceFlow id="SequenceFlow_S4" sourceRef="Task_HitungTotal" targetRef="Task_TampilkanTotal" />
    <bpmn2:sequenceFlow id="SequenceFlow_S5" sourceRef="Task_TampilkanTotal" targetRef="Task_HitungKembalian" />
    <bpmn2:sequenceFlow id="SequenceFlow_S6" sourceRef="Task_HitungKembalian" targetRef="Task_UpdateStok" />
    <bpmn2:sequenceFlow id="SequenceFlow_S7" sourceRef="Task_UpdateStok" targetRef="Task_SimpanTransaksi" />
    <bpmn2:sequenceFlow id="SequenceFlow_S8" sourceRef="Task_SimpanTransaksi" targetRef="Task_TampilkanKembalian" />
  </bpmn2:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1">
      <bpmndi:BPMNShape id="Participant_Kasir_di" bpmnElement="Participant_Kasir" isHorizontal="true">
        <dc:Bounds x="160" y="80" width="1200" height="200" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_Sistem_di" bpmnElement="Participant_Sistem" isHorizontal="true">
        <dc:Bounds x="160" y="320" width="1200" height="300" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="212" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="190" y="205" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_BukaMenuTransaksi_di" bpmnElement="Task_BukaMenuTransaksi">
        <dc:Bounds x="300" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_MasukkanJumlah_di" bpmnElement="Task_MasukkanJumlah">
        <dc:Bounds x="450" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_TerimaUang_di" bpmnElement="Task_TerimaUang">
        <dc:Bounds x="850" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_SelesaiTransaksi_di" bpmnElement="Task_SelesaiTransaksi">
        <dc:Bounds x="1100" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="1252" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1250" y="205" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="Task_ValidasiStok_di" bpmnElement="Task_ValidasiStok">
        <dc:Bounds x="450" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1_di" bpmnElement="Gateway_1" isMarkerVisible="true">
        <dc:Bounds x="595" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="585" y="452" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_HitungTotal_di" bpmnElement="Task_HitungTotal">
        <dc:Bounds x="700" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_TampilkanTotal_di" bpmnElement="Task_TampilkanTotal">
        <dc:Bounds x="850" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_HitungKembalian_di" bpmnElement="Task_HitungKembalian">
        <dc:Bounds x="1000" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_UpdateStok_di" bpmnElement="Task_UpdateStok">
        <dc:Bounds x="700" y="480" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_SimpanTransaksi_di" bpmnElement="Task_SimpanTransaksi">
        <dc:Bounds x="850" y="480" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_TampilkanKembalian_di" bpmnElement="Task_TampilkanKembalian">
        <dc:Bounds x="1100" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_TampilkanError_di" bpmnElement="Task_TampilkanError">
        <dc:Bounds x="700" y="560" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNEdge id="SequenceFlow_1_di" bpmnElement="SequenceFlow_1">
        <di:waypoint x="248" y="180" />
        <di:waypoint x="300" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_2_di" bpmnElement="SequenceFlow_2">
        <di:waypoint x="400" y="180" />
        <di:waypoint x="450" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_3_di" bpmnElement="SequenceFlow_3">
        <di:waypoint x="550" y="180" />
        <di:waypoint x="850" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_4_di" bpmnElement="SequenceFlow_4">
        <di:waypoint x="950" y="180" />
        <di:waypoint x="1100" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_5_di" bpmnElement="SequenceFlow_5">
        <di:waypoint x="1200" y="180" />
        <di:waypoint x="1252" y="180" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="SequenceFlow_S1_di" bpmnElement="SequenceFlow_S1">
        <di:waypoint x="550" y="420" />
        <di:waypoint x="595" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S2_di" bpmnElement="SequenceFlow_S2">
        <di:waypoint x="620" y="395" />
        <di:waypoint x="620" y="380" />
        <di:waypoint x="700" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="628" y="385" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S3_di" bpmnElement="SequenceFlow_S3">
        <di:waypoint x="620" y="445" />
        <di:waypoint x="620" y="600" />
        <di:waypoint x="700" y="600" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="622" y="520" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S4_di" bpmnElement="SequenceFlow_S4">
        <di:waypoint x="800" y="380" />
        <di:waypoint x="850" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S5_di" bpmnElement="SequenceFlow_S5">
        <di:waypoint x="950" y="380" />
        <di:waypoint x="1000" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S6_di" bpmnElement="SequenceFlow_S6">
        <di:waypoint x="1050" y="420" />
        <di:waypoint x="1050" y="520" />
        <di:waypoint x="800" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S7_di" bpmnElement="SequenceFlow_S7">
        <di:waypoint x="800" y="520" />
        <di:waypoint x="850" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S8_di" bpmnElement="SequenceFlow_S8">
        <di:waypoint x="950" y="520" />
        <di:waypoint x="1150" y="520" />
        <di:waypoint x="1150" y="420" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="MessageFlow_1_di" bpmnElement="MessageFlow_1">
        <di:waypoint x="500" y="220" />
        <di:waypoint x="500" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="MessageFlow_2_di" bpmnElement="MessageFlow_2">
        <di:waypoint x="900" y="340" />
        <di:waypoint x="900" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="MessageFlow_3_di" bpmnElement="MessageFlow_3">
        <di:waypoint x="1150" y="340" />
        <di:waypoint x="1150" y="220" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>
