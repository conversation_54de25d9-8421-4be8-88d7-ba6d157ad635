<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_LogoutSystem" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_LogoutSystem">
    <bpmn:participant id="Participant_User" name="User" processRef="Process_User" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_ClickLogout" targetRef="Task_ProcessLogout" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_RedirectToLogin" targetRef="Task_LogoutComplete" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_User" name="User Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_Logout" name="Start Logout">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_ClickLogout" name="Click logout button">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_LogoutComplete" name="Logout complete">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_Logout" name="End">
      <bpmn:incoming>Flow_3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_Logout" targetRef="Task_ClickLogout" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_ClickLogout" targetRef="Task_LogoutComplete" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_LogoutComplete" targetRef="EndEvent_Logout" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ProcessLogout" name="Process logout">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_LogAudit" name="Log audit trail (Include)">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_RedirectToLogin" name="Redirect to login page">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ProcessLogout" targetRef="Task_LogAudit" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_LogAudit" targetRef="Task_RedirectToLogin" />
  </bpmn:process>
</bpmn:definitions>
