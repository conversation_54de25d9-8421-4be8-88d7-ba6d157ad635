<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_CatatPenjualanHarian" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_CatatPenjualanHarian">
    <bpmn:participant id="Participant_Kasir" name="Kasir" processRef="Process_Kasir" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_MasukkanData" targetRef="Task_ValidasiData" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanKonfirmasi" targetRef="Task_SelesaiCatat" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Kasir" name="Proses Kasir" isExecutable="false">
    <bpmn:startEvent id="StartEvent_CatatPenjualan" name="Mulai Catat Penjualan">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_BukaMenuPenjualan" name="Buka menu catat penjualan">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_MasukkanData" name="Masukkan data penjualan">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelesaiCatat" name="Selesai catat penjualan">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PerbaikiData" name="Perbaiki data yang salah">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_CatatPenjualan" name="Selesai">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_CatatPenjualan" targetRef="Task_BukaMenuPenjualan" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_BukaMenuPenjualan" targetRef="Task_MasukkanData" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_MasukkanData" targetRef="Task_SelesaiCatat" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_SelesaiCatat" targetRef="EndEvent_CatatPenjualan" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_SelesaiCatat" targetRef="EndEvent_CatatPenjualan" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_PerbaikiData" targetRef="Task_MasukkanData" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_PerbaikiData" targetRef="Task_MasukkanData" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_TampilkanFormPenjualan" name="Tampilkan form penjualan">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidasiData" name="Validasi data penjualan">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_DataValid" name="Data valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_SimpanPenjualan" name="Simpan data penjualan">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CatatAudit" name="Catat log audit (Include)">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanKonfirmasi" name="Tampilkan konfirmasi berhasil">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanError" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_TampilkanFormPenjualan" targetRef="Task_ValidasiData" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidasiData" targetRef="Gateway_DataValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Ya" sourceRef="Gateway_DataValid" targetRef="Task_SimpanPenjualan" />
    <bpmn:sequenceFlow id="Flow_S4" name="Tidak" sourceRef="Gateway_DataValid" targetRef="Task_TampilkanError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_SimpanPenjualan" targetRef="Task_CatatAudit" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_CatatAudit" targetRef="Task_TampilkanKonfirmasi" />
  </bpmn:process>
</bpmn:definitions>
