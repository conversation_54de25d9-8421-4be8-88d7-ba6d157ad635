<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_LogoutSistem" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_LogoutSistem">
    <bpmn:participant id="Participant_Pengguna" name="Pengguna" processRef="Process_Pengguna" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_KlikLogout" targetRef="Task_ProsesLogout" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ArahkanKeLogin" targetRef="Task_SelesaiLogout" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Pengguna" name="Proses Pengguna" isExecutable="false">
    <bpmn:startEvent id="StartEvent_Logout" name="Mulai Logout">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_KlikLogout" name="Klik tombol logout">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelesaiLogout" name="Selesai logout">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_Logout" name="Selesai">
      <bpmn:incoming>Flow_3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_Logout" targetRef="Task_KlikLogout" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_KlikLogout" targetRef="Task_SelesaiLogout" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SelesaiLogout" targetRef="EndEvent_Logout" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_ProsesLogout" name="Proses logout">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CatatAudit" name="Catat log audit (Include)">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ArahkanKeLogin" name="Arahkan ke halaman login">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ProsesLogout" targetRef="Task_CatatAudit" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_CatatAudit" targetRef="Task_ArahkanKeLogin" />
  </bpmn:process>
</bpmn:definitions>
