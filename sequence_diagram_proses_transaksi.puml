@startuml Sequence_Proses_Transaksi
title Sequence Diagram - Proses <PERSON> (Include Catat Penjualan Harian)

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor User
participant ":TransaksiPage" as TP
participant ":TransaksiController" as TC
participant ":PenjualanController" as PC
participant ":Database" as DB

User -> TP: 1. initiateTransaction()
activate TP

TP -> User: 2. displayTransactionForm()

User -> TP: 3. inputTransactionData(itemList, totalAmount)

TP -> TC: 4. processTransaction(itemList, totalAmount)
activate TC

TC -> DB: 4.1. validateItemAvailability(itemList)
activate DB
DB --> TC: 4.2. availabilityResult()
deactivate DB

alt itemsAvailable
    TC -> DB: 4.3. calculateTotalPrice(itemList)
    activate DB
    DB --> TC: 4.4. totalPrice()
    deactivate DB
    
    TC -> DB: 4.5. saveTransaction(transactionData)
    activate DB
    DB --> TC: 4.6. transactionSaved()
    deactivate DB
    
    TC -> PC: 4.7. recordDailySales(transactionData)
    activate PC
    note right: Include relationship
    
    PC -> DB: 4.8. updateDailySalesRecord(salesData)
    activate DB
    DB --> PC: 4.9. salesRecorded()
    deactivate DB
    
    PC --> TC: 4.10. salesRecordingComplete()
    deactivate PC
    
    TC -> DB: 4.11. updateStokAfterSale(itemList)
    activate DB
    DB --> TC: 4.12. stokUpdated()
    deactivate DB
    
    TC --> TP: 4.13. transactionSuccess()
    TP --> User: 4.14. showTransactionReceipt()
else itemsNotAvailable
    TC --> TP: 4.15. transactionFailed()
    TP --> User: 4.16. showStockError()
end

deactivate TC
deactivate TP

@enduml
