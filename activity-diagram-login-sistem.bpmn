<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_LoginSystem" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_LoginSystem">
    <bpmn:participant id="Participant_User" name="User" processRef="Process_User" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_EnterCredentials" targetRef="Task_ValidateLogin" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowDashboard" targetRef="Task_UseSystem" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_ShowError" targetRef="Task_TryAgain" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_User" name="User Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_Login" name="Start Login">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenLoginPage" name="Open login page">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_EnterCredentials" name="Enter username and password">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_UseSystem" name="Use system">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_TryAgain" name="Try login again">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_Login" name="End">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_Login" targetRef="Task_OpenLoginPage" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenLoginPage" targetRef="Task_EnterCredentials" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_EnterCredentials" targetRef="Task_UseSystem" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_UseSystem" targetRef="EndEvent_Login" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_UseSystem" targetRef="EndEvent_Login" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_TryAgain" targetRef="Task_EnterCredentials" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_TryAgain" targetRef="Task_EnterCredentials" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowLoginForm" name="Show login form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateLogin" name="Validate credentials">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_Valid" name="Credentials valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_LogAuditSuccess" name="Log audit success (Include)">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowDashboard" name="Show dashboard">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_LogAuditFailure" name="Log audit failure (Include)">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowError" name="Show error message">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowLoginForm" targetRef="Task_ValidateLogin" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateLogin" targetRef="Gateway_Valid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_Valid" targetRef="Task_LogAuditSuccess" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_Valid" targetRef="Task_LogAuditFailure" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_LogAuditSuccess" targetRef="Task_ShowDashboard" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_LogAuditFailure" targetRef="Task_ShowError" />
  </bpmn:process>
</bpmn:definitions>
