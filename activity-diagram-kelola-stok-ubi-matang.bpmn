<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_ManageCookedPotatoStock" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_ManageCookedPotatoStock">
    <bpmn:participant id="Participant_Cashier" name="Cashier" processRef="Process_Cashier" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_EnterQuantity" targetRef="Task_ValidateInput" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowConfirmation" targetRef="Task_ManagementComplete" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Cashier" name="Cashier Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_ManageStock" name="Start Manage Stock">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenStockMenu" name="Open stock management menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectOperation" name="Select operation (add/reduce)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_EnterQuantity" name="Enter sweet potato quantity">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ManagementComplete" name="Stock management complete">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_TryAgain" name="Try again with valid input">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_ManageStock" name="End">
      <bpmn:incoming>Flow_7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_ManageStock" targetRef="Task_OpenStockMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenStockMenu" targetRef="Task_SelectOperation" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SelectOperation" targetRef="Task_EnterQuantity" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_EnterQuantity" targetRef="Task_ManagementComplete" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_ManagementComplete" targetRef="EndEvent_ManageStock" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_TryAgain" targetRef="Task_EnterQuantity" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_ManagementComplete" targetRef="EndEvent_ManageStock" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_TryAgain" targetRef="Task_EnterQuantity" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowStockForm" name="Show stock management form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateInput" name="Validate input">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_InputValid" name="Input valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_UpdateStock" name="Update cooked potato stock">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_LogAudit" name="Log audit trail (Include)">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowConfirmation" name="Show success confirmation">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowError" name="Show error message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowStockForm" targetRef="Task_ValidateInput" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateInput" targetRef="Gateway_InputValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_InputValid" targetRef="Task_UpdateStock" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_InputValid" targetRef="Task_ShowError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_UpdateStock" targetRef="Task_LogAudit" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_LogAudit" targetRef="Task_ShowConfirmation" />
  </bpmn:process>
</bpmn:definitions>
