@startuml
skinparam backgroundColor #ffffff
skinparam usecase {
  BackgroundColor #bbdefb
  BorderColor black
  ArrowColor black
}
skinparam linetype ortho ' memastikan garis lurus

top to bottom direction
title Use Case Diagram – Sistem Ubi Bakar Cilembu (Final UML, Rapi, Lurus)

'=== Aktor dan <PERSON> ===
actor "User" as User
actor "<PERSON><PERSON><PERSON>" as <PERSON><PERSON><PERSON>
actor "<PERSON><PERSON>" as <PERSON><PERSON> -up-|> User
Admin -down-|> User

rectangle "Sistem Ubi Bakar Cilembu" {

  '=== Shared Use Cases (oleh semua via User) ===
  (Login Sistem) as UC1
  (Logout Sistem) as UC2
  (Ke<PERSON>la Stok Ubi Matang) as UC3
  (Lihat Stok Real-time) as UC4
  (Catat Penjualan Harian) as UC5
  (Proses Transaksi) as UC6
  (Generate Lapor<PERSON> Harian) as UC7

  '=== Admin-Only Use Cases ===
  (Kelola Stok Ubi Mentah) as UC8
  (Generate Laporan Bulanan) as UC9
  (<PERSON><PERSON><PERSON>an Profit) as UC10
  (<PERSON><PERSON>la User) as UC11
  (Audit Trail) as UC12
}

'=== Relasi User ke Shared Use Case ===
User --> UC1
User --> UC2
User --> UC3
User --> UC4
User --> UC5
User --> UC6
User --> UC7

'=== Relasi Admin ke Use Case Khusus ===
Admin --> UC8
Admin --> UC9
Admin --> UC10
Admin --> UC11
Admin --> UC12

'=== INCLUDE Relationships (garis titik-titik lurus) ===
UC1 ..> UC12 : <<include>>
UC2 ..> UC12 : <<include>>
UC11 ..> UC12 : <<include>>
UC6 ..> UC5 : <<include>>
UC8 ..> UC4 : <<include>>
UC3 ..> UC4 : <<include>>
UC9 ..> UC7 : <<include>>

'=== EXTEND Relationships (jika ada perluasan bersyarat) ===
UC9 ..> UC10 : <<extend>>

@enduml
