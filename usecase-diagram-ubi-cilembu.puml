@startuml UseCase_Diagram_Ubi_Cilembu
!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E8F4FD
    BorderColor #2196F3
    FontSize 12
    FontStyle bold
}
skinparam usecase {
    BackgroundColor #F5F5F5
    BorderColor #666666
    FontSize 10
}
skinparam package {
    BackgroundColor #FAFAFA
    BorderColor #CCCCCC
    FontSize 11
    FontStyle bold
}

title **Use Case Diagram - Sistem Informasi Manajemen Ubi Bakar Cilembu**

' Actors
actor "Admin" as admin #LightBlue
actor "<PERSON><PERSON><PERSON>" as employee #LightGreen

' System boundary
rectangle "Sistem Ubi Bakar Cilembu" {
    
    ' Authentication Package
    package "Authentication System" {
        usecase "Login ke Sistem" as UC001
        usecase "Logout dari Sistem" as UC002
        usecase "Ganti Password" as UC003
    }
    
    ' Dashboard Package
    package "Dashboard & Analytics" {
        usecase "Lihat Dashboard Admin" as UC004
        usecase "Lihat Dashboard Karyawan" as UC005
        usecase "Lihat Grafik Penjualan" as UC006
        usecase "Lihat Laporan Real-time" as UC007
        usecase "Export Dashboard Data" as UC008
    }
    
    ' Inventory Management Package
    package "Manajemen Inventory" {
        usecase "Kelola Raw Inventory" as UC009
        usecase "Kelola Processed Inventory" as UC010
        usecase "Kelola Other Products" as UC011
        usecase "Tambah Stok Ubi Mentah" as UC012
        usecase "Proses Ubi Mentah ke Matang" as UC013
        usecase "Lihat Alert Stok Rendah" as UC014
        usecase "Kelola Supplier" as UC015
        usecase "Track Expiry Date" as UC016
    }
    
    ' POS System Package
    package "Point of Sale (POS)" {
        usecase "Buat Transaksi Penjualan" as UC017
        usecase "Pilih Produk" as UC018
        usecase "Hitung Total Pembayaran" as UC019
        usecase "Proses Pembayaran Cash" as UC020
        usecase "Proses Pembayaran Digital" as UC021
        usecase "Cetak Struk Digital" as UC022
        usecase "Batalkan Transaksi" as UC023
    }
    
    ' Payment Gateway Package
    package "Payment Gateway" {
        usecase "Integrasi Midtrans" as UC024
        usecase "Proses Pembayaran QRIS" as UC025
        usecase "Proses Transfer Bank" as UC026
        usecase "Proses E-Wallet" as UC027
        usecase "Verifikasi Status Pembayaran" as UC028
    }
    
    ' Production Management Package
    package "Manajemen Produksi" {
        usecase "Kelola Proses Produksi" as UC029
        usecase "Buat Batch Produksi" as UC030
        usecase "Quality Control" as UC031
        usecase "Hitung Biaya Produksi" as UC032
        usecase "Track Production Log" as UC033
    }
    
    ' Distribution Management Package
    package "Manajemen Distribusi" {
        usecase "Kelola Distribusi" as UC034
        usecase "Buat Rencana Distribusi" as UC035
        usecase "Track Status Pengiriman" as UC036
        usecase "Kelola Return Produk" as UC037
        usecase "Analisis Performa Pasar" as UC038
    }
    
    ' Financial Reporting Package
    package "Laporan Keuangan" {
        usecase "Generate Laporan Harian" as UC039
        usecase "Generate Laporan Bulanan" as UC040
        usecase "Lihat Profit & Loss" as UC041
        usecase "Lihat Cash Flow" as UC042
        usecase "Export Laporan PDF" as UC043
        usecase "Export Laporan Excel" as UC044
        usecase "Proyeksi Keuangan" as UC045
    }
    
    ' User Management Package (Admin Only)
    package "Manajemen User" {
        usecase "Kelola Data User" as UC046
        usecase "Tambah User Baru" as UC047
        usecase "Edit Data User" as UC048
        usecase "Hapus User" as UC049
        usecase "Atur Role User" as UC050
        usecase "Lihat Audit Log" as UC051
    }
    
    ' Expense Management Package
    package "Manajemen Pengeluaran" {
        usecase "Kelola Data Expense" as UC052
        usecase "Tambah Pengeluaran" as UC053
        usecase "Kategorisasi Expense" as UC054
        usecase "Laporan Pengeluaran" as UC055
    }
    
    ' System Administration Package (Admin Only)
    package "Administrasi Sistem" {
        usecase "Konfigurasi Sistem" as UC056
        usecase "Backup Database" as UC057
        usecase "Restore Database" as UC058
        usecase "Monitor Performance" as UC059
        usecase "Kelola Cache" as UC060
    }
}

' Actor-UseCase Relationships

' Admin relationships (Full Access)
admin --> UC001
admin --> UC002
admin --> UC003
admin --> UC004
admin --> UC006
admin --> UC007
admin --> UC008
admin --> UC009
admin --> UC010
admin --> UC011
admin --> UC012
admin --> UC013
admin --> UC014
admin --> UC015
admin --> UC016
admin --> UC017
admin --> UC018
admin --> UC019
admin --> UC020
admin --> UC021
admin --> UC022
admin --> UC023
admin --> UC024
admin --> UC025
admin --> UC026
admin --> UC027
admin --> UC028
admin --> UC029
admin --> UC030
admin --> UC031
admin --> UC032
admin --> UC033
admin --> UC034
admin --> UC035
admin --> UC036
admin --> UC037
admin --> UC038
admin --> UC039
admin --> UC040
admin --> UC041
admin --> UC042
admin --> UC043
admin --> UC044
admin --> UC045
admin --> UC046
admin --> UC047
admin --> UC048
admin --> UC049
admin --> UC050
admin --> UC051
admin --> UC052
admin --> UC053
admin --> UC054
admin --> UC055
admin --> UC056
admin --> UC057
admin --> UC058
admin --> UC059
admin --> UC060

' Employee relationships (Limited Access)
employee --> UC001
employee --> UC002
employee --> UC003
employee --> UC005
employee --> UC006
employee --> UC014
employee --> UC017
employee --> UC018
employee --> UC019
employee --> UC020
employee --> UC021
employee --> UC022
employee --> UC023
employee --> UC024
employee --> UC025
employee --> UC026
employee --> UC027
employee --> UC028
employee --> UC039

@enduml
