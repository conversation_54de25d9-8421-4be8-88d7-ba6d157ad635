@startuml Sequence_Melihat_Mencari_Produk
title Sequence Diagram - Melihat dan <PERSON>cari Produk

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor Customer
participant ":ProductPage" as PP
participant ":ProductController" as PC
participant ":Database" as DB

Customer -> PP: 1. accessProductPage()
activate PP

PP -> PC: 2. loadAllProducts()
activate PC

PC -> DB: 2.1. getAllProducts()
activate DB
DB --> PC: 2.2. productList()
deactivate DB

PC --> PP: 2.3. displayProducts()
PP --> Customer: 2.4. showProductCatalog()

Customer -> PP: 3. searchProduct(keyword)

PP -> PC: 4. searchProducts(keyword)

PC -> DB: 4.1. findProductsByKeyword(keyword)
activate DB
DB --> PC: 4.2. searchResults()
deactivate DB

PC --> PP: 4.3. displaySearchResults()
PP --> Customer: 4.4. showSearchResults()

Customer -> PP: 5. selectProduct(productId)

PP -> PC: 6. getProductDetails(productId)

PC -> DB: 6.1. getProductById(productId)
activate DB
DB --> PC: 6.2. productDetails()
deactivate DB

PC --> PP: 6.3. displayProductDetails()
PP --> Customer: 6.4. showProductDetails()

deactivate PC
deactivate PP

@enduml
