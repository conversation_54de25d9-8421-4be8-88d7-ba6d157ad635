<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_TransactionProcess" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_TransactionProcess">
    <bpmn:participant id="Participant_Cashier" name="Cashier" processRef="Process_Cashier" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_EnterQuantity" targetRef="Task_ValidateStock" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowTotal" targetRef="Task_ReceivePayment" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_ShowChange" targetRef="Task_CompleteTransaction" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Cashier" name="Cashier Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_Transaction" name="Start Transaction">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenTransaction" name="Open transaction menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_EnterQuantity" name="Enter sweet potato quantity">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ReceivePayment" name="Receive payment from customer">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_CompleteTransaction" name="Complete transaction">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_8</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_TryAgain" name="Try again with valid quantity">
      <bpmn:incoming>Flow_9</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_Transaction" name="End">
      <bpmn:incoming>Flow_8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_Transaction" targetRef="Task_OpenTransaction" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenTransaction" targetRef="Task_EnterQuantity" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_EnterQuantity" targetRef="Task_ReceivePayment" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_ReceivePayment" targetRef="Task_CompleteTransaction" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_CompleteTransaction" targetRef="EndEvent_Transaction" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_TryAgain" targetRef="Task_EnterQuantity" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_CompleteTransaction" targetRef="EndEvent_Transaction" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_CompleteTransaction" targetRef="EndEvent_Transaction" />
    <bpmn:sequenceFlow id="Flow_9" sourceRef="Task_TryAgain" targetRef="Task_EnterQuantity" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowTransactionForm" name="Show transaction form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateStock" name="Validate stock availability">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_StockSufficient" name="Stock sufficient?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_CalculateTotal" name="Calculate total price">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowTotal" name="Show total price">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CalculateChange" name="Calculate change">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_UpdateStock" name="Update sweet potato stock">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_SaveTransaction" name="Save transaction data">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowChange" name="Show change amount">
      <bpmn:incoming>Flow_S9</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowStockError" name="Show insufficient stock message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowTransactionForm" targetRef="Task_ValidateStock" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateStock" targetRef="Gateway_StockSufficient" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_StockSufficient" targetRef="Task_CalculateTotal" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_StockSufficient" targetRef="Task_ShowStockError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_CalculateTotal" targetRef="Task_ShowTotal" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_ShowTotal" targetRef="Task_CalculateChange" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_CalculateChange" targetRef="Task_UpdateStock" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_UpdateStock" targetRef="Task_SaveTransaction" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_SaveTransaction" targetRef="Task_ShowChange" />
  </bpmn:process>
</bpmn:definitions>
