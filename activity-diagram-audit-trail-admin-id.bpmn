<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_AuditTrailAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_AuditTrailAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_PilihFilter" targetRef="Task_ValidasiFilter" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanAuditTrail" targetRef="Task_LihatAuditTrail" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_ExportAuditTrail" targetRef="Task_UnduhAuditTrail" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Proses Admin" isExecutable="false">
    <bpmn:startEvent id="StartEvent_AuditTrail" name="Mulai Audit Trail">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_BukaMenuAuditTrail" name="Buka menu audit trail">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihFilter" name="Pilih filter (tanggal, user, aktivitas)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_LihatAuditTrail" name="Lihat audit trail">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_ExportAudit" name="Export audit trail?">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_UnduhAuditTrail" name="Unduh file audit trail">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_10</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PerbaikiFilter" name="Perbaiki filter">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_AuditTrail" name="Selesai">
      <bpmn:incoming>Flow_9</bpmn:incoming>
      <bpmn:incoming>Flow_10</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_AuditTrail" targetRef="Task_BukaMenuAuditTrail" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_BukaMenuAuditTrail" targetRef="Task_PilihFilter" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_PilihFilter" targetRef="Task_LihatAuditTrail" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_LihatAuditTrail" targetRef="Gateway_ExportAudit" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_LihatAuditTrail" targetRef="Gateway_ExportAudit" />
    <bpmn:sequenceFlow id="Flow_6" name="Ya" sourceRef="Gateway_ExportAudit" targetRef="Task_UnduhAuditTrail" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_PerbaikiFilter" targetRef="Task_PilihFilter" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_PerbaikiFilter" targetRef="Task_PilihFilter" />
    <bpmn:sequenceFlow id="Flow_9" name="Tidak" sourceRef="Gateway_ExportAudit" targetRef="EndEvent_AuditTrail" />
    <bpmn:sequenceFlow id="Flow_10" sourceRef="Task_UnduhAuditTrail" targetRef="EndEvent_AuditTrail" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_TampilkanFormAuditTrail" name="Tampilkan form audit trail">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidasiFilter" name="Validasi filter">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_FilterValid" name="Filter valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_AmbilDataAudit" name="Ambil data audit trail">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FilterDataAudit" name="Filter data sesuai kriteria">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatTampilanAudit" name="Format tampilan audit trail">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanAuditTrail" name="Tampilkan audit trail">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_GenerateFileAudit" name="Generate file audit trail">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ExportAuditTrail" name="Export audit trail">
      <bpmn:incoming>Flow_S9</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanErrorAudit" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_TampilkanFormAuditTrail" targetRef="Task_ValidasiFilter" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidasiFilter" targetRef="Gateway_FilterValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Ya" sourceRef="Gateway_FilterValid" targetRef="Task_AmbilDataAudit" />
    <bpmn:sequenceFlow id="Flow_S4" name="Tidak" sourceRef="Gateway_FilterValid" targetRef="Task_TampilkanErrorAudit" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_AmbilDataAudit" targetRef="Task_FilterDataAudit" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_FilterDataAudit" targetRef="Task_FormatTampilanAudit" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_FormatTampilanAudit" targetRef="Task_TampilkanAuditTrail" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_TampilkanAuditTrail" targetRef="Task_GenerateFileAudit" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_GenerateFileAudit" targetRef="Task_ExportAuditTrail" />
  </bpmn:process>
</bpmn:definitions>
