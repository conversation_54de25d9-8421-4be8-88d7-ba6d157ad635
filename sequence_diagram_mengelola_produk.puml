@startuml Mengelola Produk (Admin)
!theme plain
skinparam backgroundColor #FFFFFF
skinparam participant {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #000000
}
skinparam arrow {
    Color #1976D2
    FontColor #000000
}

title Sequence Diagram - Mengelola Produk (Admin)

actor Admin
participant "Admin Dashboard" as AdminView
participant "Product Controller" as ProductController
participant "Product Database" as ProductDB

== Melihat Daftar Produk ==
Admin -> AdminView : 1. Akses menu produk
AdminView -> ProductController : 2. Request daftar produk
ProductController -> ProductDB : 3. Query semua produk
ProductDB -> ProductController : 4. Return data produk
ProductController -> AdminView : 5. Kirim data produk
AdminView -> Admin : 6. <PERSON>pi<PERSON><PERSON> daftar produk

== Menambah Produk Baru ==
Admin -> AdminView : 7. <PERSON><PERSON> "Tambah Produk"
AdminView -> Admin : 8. Tampilkan form produk
Admin -> AdminView : 9. Input data produk baru
AdminView -> ProductController : 10. Kirim data produk
ProductController -> ProductDB : 11. Simpan produk baru
ProductDB -> ProductController : 12. Konfirmasi penyimpanan
ProductController -> AdminView : 13. Produk berhasil ditambah
AdminView -> Admin : 14. Tampilkan pesan sukses

== Mengupdate Produk ==
Admin -> AdminView : 15. Klik "Edit Produk"
AdminView -> ProductController : 16. Request data produk
ProductController -> ProductDB : 17. Query data produk
ProductDB -> ProductController : 18. Return data produk
ProductController -> AdminView : 19. Kirim data produk
AdminView -> Admin : 20. Tampilkan form edit

Admin -> AdminView : 21. Update data produk
AdminView -> ProductController : 22. Kirim data update
ProductController -> ProductDB : 23. Update data produk
ProductDB -> ProductController : 24. Konfirmasi update
ProductController -> AdminView : 25. Update berhasil
AdminView -> Admin : 26. Tampilkan pesan sukses

== Menghapus Produk ==
Admin -> AdminView : 27. Klik "Hapus Produk"
AdminView -> Admin : 28. Konfirmasi penghapusan
Admin -> AdminView : 29. Konfirmasi hapus
AdminView -> ProductController : 30. Request hapus produk
ProductController -> ProductDB : 31. Hapus data produk
ProductDB -> ProductController : 32. Konfirmasi penghapusan
ProductController -> AdminView : 33. Produk berhasil dihapus
AdminView -> Admin : 34. Tampilkan pesan sukses

@enduml
