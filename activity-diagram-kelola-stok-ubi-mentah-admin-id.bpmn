<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_KelolaStokUbiMentahAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_KelolaStokUbiMentahAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_MasukkanData" targetRef="Task_ValidasiInput" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanKonfirmasi" targetRef="Task_SelesaiKelola" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Proses Admin" isExecutable="false">
    <bpmn:startEvent id="StartEvent_KelolaStokMentah" name="Mulai Kelola Stok Mentah">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_BukaMenuStokMentah" name="Buka menu kelola stok ubi mentah">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihOperasi" name="Pilih operasi (tambah/kurang/edit)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_MasukkanData" name="Masukkan data stok ubi mentah">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelesaiKelola" name="Selesai kelola stok">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PerbaikiData" name="Perbaiki data yang salah">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_KelolaStokMentah" name="Selesai">
      <bpmn:incoming>Flow_7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_KelolaStokMentah" targetRef="Task_BukaMenuStokMentah" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_BukaMenuStokMentah" targetRef="Task_PilihOperasi" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_PilihOperasi" targetRef="Task_MasukkanData" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_MasukkanData" targetRef="Task_SelesaiKelola" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_SelesaiKelola" targetRef="EndEvent_KelolaStokMentah" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_PerbaikiData" targetRef="Task_MasukkanData" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_SelesaiKelola" targetRef="EndEvent_KelolaStokMentah" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_PerbaikiData" targetRef="Task_MasukkanData" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_TampilkanFormStokMentah" name="Tampilkan form kelola stok mentah">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidasiInput" name="Validasi input data">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_InputValid" name="Input valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_UpdateStokMentah" name="Update stok ubi mentah">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_HitungStokMatang" name="Hitung estimasi stok matang">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_UpdateStokMatang" name="Update stok ubi matang">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CatatAudit" name="Catat log audit (Include)">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanKonfirmasi" name="Tampilkan konfirmasi berhasil">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanError" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_TampilkanFormStokMentah" targetRef="Task_ValidasiInput" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidasiInput" targetRef="Gateway_InputValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Ya" sourceRef="Gateway_InputValid" targetRef="Task_UpdateStokMentah" />
    <bpmn:sequenceFlow id="Flow_S4" name="Tidak" sourceRef="Gateway_InputValid" targetRef="Task_TampilkanError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_UpdateStokMentah" targetRef="Task_HitungStokMatang" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_HitungStokMatang" targetRef="Task_UpdateStokMatang" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_UpdateStokMatang" targetRef="Task_CatatAudit" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_CatatAudit" targetRef="Task_TampilkanKonfirmasi" />
  </bpmn:process>
</bpmn:definitions>
