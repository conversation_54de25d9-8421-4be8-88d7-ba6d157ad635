@startuml Sequence_Generate_Laporan_Harian
title Sequence Diagram - Generate Laporan <PERSON>

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor User
participant ":LaporanHarianPage" as LHP
participant ":ReportController" as RC
participant ":Database" as DB
participant ":ReportGenerator" as RG
participant ":DateTimeManager" as DTM

User -> LHP: 1. accessLaporanHarianPage()
activate LHP

LHP -> User: 2. displayDateSelection()

User -> LHP: 3. selectReportDate(selectedDate)

LHP -> RC: 4. generateDailyReport(selectedDate)
activate RC

RC -> DTM: 4.1. validateReportDate(selectedDate)
activate DTM
DTM --> RC: 4.2. dateValidation()
deactivate DTM

alt validDate
    RC -> DB: 4.3. getDailySalesData(selectedDate)
    activate DB
    DB --> RC: 4.4. dailySalesData()
    deactivate DB
    
    RC -> DB: 4.5. getDailyTransactionData(selectedDate)
    activate DB
    DB --> RC: 4.6. dailyTransactionData()
    deactivate DB
    
    RC -> DB: 4.7. getDailyStokMovement(selectedDate)
    activate DB
    DB --> RC: 4.8. dailyStokMovement()
    deactivate DB
    
    RC -> RG: 4.9. compileReportData(salesData, transactionData, stokData)
    activate RG
    
    RG -> RG: 4.10. calculateDailyTotals()
    RG -> RG: 4.11. formatReportLayout()
    
    RG --> RC: 4.12. dailyReportCompiled()
    deactivate RG
    
    RC --> LHP: 4.13. reportGenerationSuccess()
    LHP --> User: 4.14. displayDailyReport()
    
    User -> LHP: 5. downloadReport()
    
    LHP -> RC: 6. exportDailyReport(format)
    
    RC -> RG: 6.1. exportReport(format)
    activate RG
    RG --> RC: 6.2. reportExported()
    deactivate RG
    
    RC --> LHP: 6.3. downloadReady()
    LHP --> User: 6.4. initiateDownload()
else invalidDate
    RC --> LHP: 4.15. reportGenerationFailed()
    LHP --> User: 4.16. showDateError()
end

deactivate RC
deactivate LHP

@enduml
