<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_TampilkanProfitAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_TampilkanProfitAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_PilihPeriode" targetRef="Task_ValidasiPeriode" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanProfit" targetRef="Task_LihatProfit" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Proses Admin" isExecutable="false">
    <bpmn:startEvent id="StartEvent_TampilkanProfit" name="Mulai Tampilkan Profit">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_BukaMenuProfit" name="Buka menu tampilkan profit">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihPeriode" name="Pilih periode (harian/bulanan)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_LihatProfit" name="Lihat informasi profit">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihPeriodeLain" name="Pilih periode yang valid">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_TampilkanProfit" name="Selesai">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_TampilkanProfit" targetRef="Task_BukaMenuProfit" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_BukaMenuProfit" targetRef="Task_PilihPeriode" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_PilihPeriode" targetRef="Task_LihatProfit" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_LihatProfit" targetRef="EndEvent_TampilkanProfit" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_LihatProfit" targetRef="EndEvent_TampilkanProfit" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_PilihPeriodeLain" targetRef="Task_PilihPeriode" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_PilihPeriodeLain" targetRef="Task_PilihPeriode" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_TampilkanFormProfit" name="Tampilkan form profit">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidasiPeriode" name="Validasi periode">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_PeriodeValid" name="Periode valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_AmbilDataPenjualan" name="Ambil data penjualan">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_HitungPendapatan" name="Hitung total pendapatan">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_HitungBiaya" name="Hitung total biaya">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_HitungProfit" name="Hitung profit bersih">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatTampilan" name="Format tampilan profit">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanProfit" name="Tampilkan informasi profit">
      <bpmn:incoming>Flow_S9</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanError" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_TampilkanFormProfit" targetRef="Task_ValidasiPeriode" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidasiPeriode" targetRef="Gateway_PeriodeValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Ya" sourceRef="Gateway_PeriodeValid" targetRef="Task_AmbilDataPenjualan" />
    <bpmn:sequenceFlow id="Flow_S4" name="Tidak" sourceRef="Gateway_PeriodeValid" targetRef="Task_TampilkanError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_AmbilDataPenjualan" targetRef="Task_HitungPendapatan" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_HitungPendapatan" targetRef="Task_HitungBiaya" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_HitungBiaya" targetRef="Task_HitungProfit" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_HitungProfit" targetRef="Task_FormatTampilan" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_FormatTampilan" targetRef="Task_TampilkanProfit" />
  </bpmn:process>
</bpmn:definitions>
