@startuml
!theme plain
skinparam backgroundColor #ffffff
skinparam entity {
  BackgroundColor #e3f2fd
  BorderColor #1976d2
  FontColor black
}
skinparam linetype ortho

title Entity Relationship Diagram - Sistem Ubi Bakar Cilembu

' === ENTITIES ===

entity "users" as users {
  * id : BIGINT <<PK>>
  --
  * name : VARCHAR(255)
  * email : VARCHAR(255) <<UNIQUE>>
  email_verified_at : TIMESTAMP
  * password : VARCHAR(255)
  * role : ENUM('admin', 'karyawan')
  remember_token : VARCHAR(100)
  last_activity : TIMESTAMP
  * created_at : TIMESTAMP
  * updated_at : TIMESTAMP
  deleted_at : TIMESTAMP
}

entity "suppliers" as suppliers {
  * id : BIGINT <<PK>>
  --
  * name : VARCHA<PERSON>(255)
  contact_person : VARCHA<PERSON>(255)
  phone_number : VARCHAR(255)
  email : VARCHAR(255)
  address : TEXT
  notes : TEXT
  * is_active : BOOLEAN
  * created_at : TIMESTAMP
  * updated_at : TIMESTAMP
  deleted_at : TIMES<PERSON>MP
}

entity "raw_inventory" as raw_inventory {
  * id : BIGINT <<PK>>
  --
  * batch_number : VARCHAR(255) <<UNIQUE>>
  * name : VARCHAR(255)
  supplier_id : BIGINT <<FK>>
  supplier_name : VARCHAR(255)
  * quantity_kg : DECIMAL(10,2)
  * cost_per_kg : DECIMAL(10,2)
  * total_cost : DECIMAL(12,2)
  * purchase_date : DATE
  expiry_date : DATE
  * quality : ENUM('A', 'B', 'C')
  * current_stock : DECIMAL(10,2)
  min_stock_threshold : DECIMAL(10,2)
  * is_active : BOOLEAN
  notes : TEXT
  * created_at : TIMESTAMP
  * updated_at : TIMESTAMP
}

entity "processed_inventory" as processed_inventory {
  * id : BIGINT <<PK>>
  --
  * batch_number : VARCHAR(255) <<UNIQUE>>
  raw_inventory_id : BIGINT <<FK>>
  * quantity_processed_kg : DECIMAL(10,2)
  * quantity_produced : DECIMAL(10,2)
  * cost_per_unit : DECIMAL(10,2)
  * selling_price : DECIMAL(10,2)
  * production_date : DATE
  * expiry_date : DATE
  * product_type : ENUM('Original', 'Premium', 'Special')
  * current_stock : DECIMAL(10,2)
  min_stock_threshold : DECIMAL(10,2)
  priority : ENUM('low', 'medium', 'high')
  expiry_recommendation : TEXT
  * is_active : BOOLEAN
  notes : TEXT
  * created_at : TIMESTAMP
  * updated_at : TIMESTAMP
}

entity "production_logs" as production_logs {
  * id : BIGINT <<PK>>
  --
  * raw_inventory_id : BIGINT <<FK>>
  * processed_inventory_id : BIGINT <<FK>>
  user_id : BIGINT <<FK>>
  * raw_amount_used : DECIMAL(10,2)
  * produced_amount : INTEGER
  * raw_cost : DECIMAL(12,2)
  additional_cost : DECIMAL(12,2)
  * total_cost : DECIMAL(12,2)
  * cost_per_item : DECIMAL(12,2)
  * raw_name : VARCHAR(255)
  * processed_name : VARCHAR(255)
  * created_at : TIMESTAMP
  * updated_at : TIMESTAMP
}

entity "transactions" as transactions {
  * id : BIGINT <<PK>>
  --
  * invoice_number : VARCHAR(255) <<UNIQUE>>
  * user_id : BIGINT <<FK>>
  customer_name : VARCHAR(255)
  customer_phone : VARCHAR(255)
  * subtotal : DECIMAL(12,2)
  tax : DECIMAL(12,2)
  discount : DECIMAL(12,2)
  * total_amount : DECIMAL(12,2)
  * amount_paid : DECIMAL(12,2)
  change_amount : DECIMAL(12,2)
  * payment_method : ENUM('cash', 'transfer', 'qris', 'debit', 'credit')
  * status : ENUM('completed', 'cancelled', 'refunded')
  notes : TEXT
  * created_at : TIMESTAMP
  * updated_at : TIMESTAMP
}

entity "transaction_items" as transaction_items {
  * id : BIGINT <<PK>>
  --
  * transaction_id : BIGINT <<FK>>
  * product_id : BIGINT <<FK>>
  * product_name : VARCHAR(255)
  * price : DECIMAL(10,2)
  * quantity : INTEGER
  discount : DECIMAL(10,2)
  * subtotal : DECIMAL(12,2)
  * created_at : TIMESTAMP
  * updated_at : TIMESTAMP
}

entity "audit_logs" as audit_logs {
  * id : BIGINT <<PK>>
  --
  user_id : BIGINT <<FK>>
  * action : VARCHAR(255)
  * model_type : VARCHAR(255)
  * model_id : BIGINT
  old_values : TEXT
  new_values : TEXT
  ip_address : VARCHAR(255)
  user_agent : VARCHAR(255)
  * created_at : TIMESTAMP
  * updated_at : TIMESTAMP
}

' === RELATIONSHIPS ===

' Suppliers to Raw Inventory
suppliers ||--o{ raw_inventory : "supplies"

' Raw Inventory to Processed Inventory
raw_inventory ||--o{ processed_inventory : "processed_from"

' Raw Inventory to Production Logs
raw_inventory ||--o{ production_logs : "used_in"

' Processed Inventory to Production Logs
processed_inventory ||--o{ production_logs : "produced_in"

' Users to Production Logs
users ||--o{ production_logs : "logs"

' Users to Transactions
users ||--o{ transactions : "creates"

' Transactions to Transaction Items
transactions ||--o{ transaction_items : "contains"

' Processed Inventory to Transaction Items
processed_inventory ||--o{ transaction_items : "sold_as"

' Users to Audit Logs
users ||--o{ audit_logs : "audited_by"

@enduml
