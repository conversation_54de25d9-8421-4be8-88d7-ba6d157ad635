@startuml Checkout P<PERSON>an dan <PERSON>
!theme plain
skinparam backgroundColor #FFFFFF
skinparam participant {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #000000
}
skinparam arrow {
    Color #1976D2
    FontColor #000000
}

title Sequence Diagram - Checkout <PERSON><PERSON><PERSON> dan <PERSON>

actor Customer
participant "Cart View" as CartView
participant "Checkout View" as CheckoutView
participant "Order Controller" as OrderController
participant "Payment Controller" as PaymentController
participant "Cart Database" as CartDB
participant "Order Database" as OrderDB
participant "Payment Database" as PaymentDB
participant "Product Database" as ProductDB

Customer -> CartView : 1. Lihat keranjang
CartView -> CartController : 2. Request data keranjang
CartController -> CartDB : 3. Query items keranjang
CartDB -> CartController : 4. Return data keranjang
CartController -> CartView : 5. Kirim data keranjang
CartView -> Customer : 6. <PERSON><PERSON><PERSON><PERSON> items keranjang

Customer -> CartView : 7. <PERSON><PERSON> "Checkout"
CartView -> CheckoutView : 8. Redirect ke halaman checkout
CheckoutView -> Customer : 9. <PERSON><PERSON><PERSON>an form checkout

Customer -> CheckoutView : 10. Input data pengiriman & pembayaran
CheckoutView -> OrderController : 11. Kirim data pesanan
OrderController -> ProductDB : 12. Cek stok semua item
ProductDB -> OrderController : 13. Return status stok

alt Semua stok tersedia
    OrderController -> OrderDB : 14a. Buat pesanan baru
    OrderDB -> OrderController : 15a. Return order ID
    
    OrderController -> PaymentController : 16a. Process payment
    PaymentController -> PaymentDB : 17a. Simpan data pembayaran
    PaymentDB -> PaymentController : 18a. Konfirmasi pembayaran
    
    alt Pembayaran berhasil
        PaymentController -> OrderController : 19a. Payment success
        OrderController -> ProductDB : 20a. Update stok produk
        ProductDB -> OrderController : 21a. Konfirmasi update stok
        OrderController -> CartDB : 22a. Kosongkan keranjang
        CartDB -> OrderController : 23a. Konfirmasi pengosongan
        OrderController -> CheckoutView : 24a. Checkout berhasil
        CheckoutView -> Customer : 25a. Tampilkan konfirmasi pesanan
    else Pembayaran gagal
        PaymentController -> OrderController : 19b. Payment failed
        OrderController -> OrderDB : 20b. Cancel pesanan
        OrderDB -> OrderController : 21b. Konfirmasi cancel
        OrderController -> CheckoutView : 22b. Checkout gagal
        CheckoutView -> Customer : 23b. Tampilkan error pembayaran
    end
else Stok tidak cukup
    OrderController -> CheckoutView : 14b. Stok tidak cukup
    CheckoutView -> Customer : 15b. Tampilkan error stok
end

@enduml
