<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_ManageUserAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_ManageUserAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_EnterUserData" targetRef="Task_ValidateUserData" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowConfirmation" targetRef="Task_ManagementComplete" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Admin Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_ManageUser" name="Start Manage User">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenUserMenu" name="Open user management menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectUserOperation" name="Select operation (add/edit/delete)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_EnterUserData" name="Enter user data">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ManagementComplete" name="User management complete">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_CorrectUserData" name="Correct user data">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_ManageUser" name="End">
      <bpmn:incoming>Flow_7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_ManageUser" targetRef="Task_OpenUserMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenUserMenu" targetRef="Task_SelectUserOperation" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SelectUserOperation" targetRef="Task_EnterUserData" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_EnterUserData" targetRef="Task_ManagementComplete" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_ManagementComplete" targetRef="EndEvent_ManageUser" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_CorrectUserData" targetRef="Task_EnterUserData" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_ManagementComplete" targetRef="EndEvent_ManageUser" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_CorrectUserData" targetRef="Task_EnterUserData" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowUserForm" name="Show user management form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateUserData" name="Validate user data">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_UserDataValid" name="User data valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_UserOperation" name="Operation type?">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_AddUser" name="Add new user">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_EditUser" name="Edit user data">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_DeleteUser" name="Delete user">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S10</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_LogUserAudit" name="Log user audit trail (Include)">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:incoming>Flow_S9</bpmn:incoming>
      <bpmn:incoming>Flow_S10</bpmn:incoming>
      <bpmn:outgoing>Flow_S11</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowConfirmation" name="Show success confirmation">
      <bpmn:incoming>Flow_S11</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowUserError" name="Show error message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowUserForm" targetRef="Task_ValidateUserData" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateUserData" targetRef="Gateway_UserDataValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_UserDataValid" targetRef="Gateway_UserOperation" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_UserDataValid" targetRef="Task_ShowUserError" />
    <bpmn:sequenceFlow id="Flow_S5" name="Add" sourceRef="Gateway_UserOperation" targetRef="Task_AddUser" />
    <bpmn:sequenceFlow id="Flow_S6" name="Edit" sourceRef="Gateway_UserOperation" targetRef="Task_EditUser" />
    <bpmn:sequenceFlow id="Flow_S7" name="Delete" sourceRef="Gateway_UserOperation" targetRef="Task_DeleteUser" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_AddUser" targetRef="Task_LogUserAudit" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_EditUser" targetRef="Task_LogUserAudit" />
    <bpmn:sequenceFlow id="Flow_S10" sourceRef="Task_DeleteUser" targetRef="Task_LogUserAudit" />
    <bpmn:sequenceFlow id="Flow_S11" sourceRef="Task_LogUserAudit" targetRef="Task_ShowConfirmation" />
  </bpmn:process>
</bpmn:definitions>
