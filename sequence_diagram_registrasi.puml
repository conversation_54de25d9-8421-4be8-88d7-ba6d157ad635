@startuml Sequence_Registrasi_Process
title Sequence Diagram - Registrasi Process

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor Customer
participant ":RegisterPage" as RP
participant ":AuthController" as AC
participant ":Database" as DB

Customer -> RP: 1. accessRegisterPage()
activate RP

RP -> Customer: 2. displayRegistrationForm()

Customer -> RP: 3. inputRegistrationData(userData)

RP -> AC: 4. registerUser(userData)
activate AC

AC -> DB: 4.1. checkUserExists(email, username)
activate DB
DB --> AC: 4.2. userExistsResult()
deactivate DB

alt userNotExists
    AC -> DB: 4.3. validateUserData(userData)
    activate DB
    DB --> AC: 4.4. validationResult()
    deactivate DB

    alt dataValid
        AC -> DB: 4.5. save<PERSON><PERSON><PERSON><PERSON>(userData)
        activate DB
        DB --> AC: 4.6. userSaved()
        deactivate DB

        AC --> RP: 4.7. registrationSuccess()
        RP --> Customer: 4.8. showSuccessMessage()
    else dataInvalid
        AC --> RP: 4.9. registrationFailed()
        RP --> Customer: 4.10. showValidationError()
    end
else userExists
    AC --> RP: 4.11. userAlreadyExists()
    RP --> Customer: 4.12. showUserExistsError()
end

deactivate AC
deactivate RP

@enduml
