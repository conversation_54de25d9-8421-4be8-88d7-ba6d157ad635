<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_AuditTrailAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_AuditTrailAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_SelectFilter" targetRef="Task_ValidateFilter" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowAuditTrail" targetRef="Task_ViewAuditTrail" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_ExportAuditTrail" targetRef="Task_DownloadAuditTrail" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Admin Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_AuditTrail" name="Start Audit Trail">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenAuditTrailMenu" name="Open audit trail menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectFilter" name="Select filter (date, user, activity)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ViewAuditTrail" name="View audit trail">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_ExportAudit" name="Export audit trail?">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_DownloadAuditTrail" name="Download audit trail file">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_10</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_CorrectFilter" name="Correct filter">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_AuditTrail" name="End">
      <bpmn:incoming>Flow_9</bpmn:incoming>
      <bpmn:incoming>Flow_10</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_AuditTrail" targetRef="Task_OpenAuditTrailMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenAuditTrailMenu" targetRef="Task_SelectFilter" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SelectFilter" targetRef="Task_ViewAuditTrail" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_ViewAuditTrail" targetRef="Gateway_ExportAudit" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_ViewAuditTrail" targetRef="Gateway_ExportAudit" />
    <bpmn:sequenceFlow id="Flow_6" name="Yes" sourceRef="Gateway_ExportAudit" targetRef="Task_DownloadAuditTrail" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_CorrectFilter" targetRef="Task_SelectFilter" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_CorrectFilter" targetRef="Task_SelectFilter" />
    <bpmn:sequenceFlow id="Flow_9" name="No" sourceRef="Gateway_ExportAudit" targetRef="EndEvent_AuditTrail" />
    <bpmn:sequenceFlow id="Flow_10" sourceRef="Task_DownloadAuditTrail" targetRef="EndEvent_AuditTrail" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowAuditTrailForm" name="Show audit trail form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateFilter" name="Validate filter">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_FilterValid" name="Filter valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_GetAuditData" name="Get audit trail data">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FilterAuditData" name="Filter data by criteria">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatAuditDisplay" name="Format audit trail display">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowAuditTrail" name="Show audit trail">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_GenerateAuditFile" name="Generate audit trail file">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ExportAuditTrail" name="Export audit trail">
      <bpmn:incoming>Flow_S9</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowAuditError" name="Show error message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowAuditTrailForm" targetRef="Task_ValidateFilter" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateFilter" targetRef="Gateway_FilterValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_FilterValid" targetRef="Task_GetAuditData" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_FilterValid" targetRef="Task_ShowAuditError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_GetAuditData" targetRef="Task_FilterAuditData" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_FilterAuditData" targetRef="Task_FormatAuditDisplay" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_FormatAuditDisplay" targetRef="Task_ShowAuditTrail" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_ShowAuditTrail" targetRef="Task_GenerateAuditFile" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_GenerateAuditFile" targetRef="Task_ExportAuditTrail" />
  </bpmn:process>
</bpmn:definitions>
