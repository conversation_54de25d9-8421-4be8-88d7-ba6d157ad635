<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_KelolaUserAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_KelolaUserAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_MasukkanDataUser" targetRef="Task_ValidasiDataUser" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanKonfirmasi" targetRef="Task_SelesaiKelola" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Proses Admin" isExecutable="false">
    <bpmn:startEvent id="StartEvent_KelolaUser" name="Mulai Kelola User">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_BukaMenuUser" name="Buka menu kelola user">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihOperasiUser" name="Pilih operasi (tambah/edit/hapus)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_MasukkanDataUser" name="Masukkan data user">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelesaiKelola" name="Selesai kelola user">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PerbaikiDataUser" name="Perbaiki data user">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_KelolaUser" name="Selesai">
      <bpmn:incoming>Flow_7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_KelolaUser" targetRef="Task_BukaMenuUser" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_BukaMenuUser" targetRef="Task_PilihOperasiUser" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_PilihOperasiUser" targetRef="Task_MasukkanDataUser" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_MasukkanDataUser" targetRef="Task_SelesaiKelola" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_SelesaiKelola" targetRef="EndEvent_KelolaUser" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_PerbaikiDataUser" targetRef="Task_MasukkanDataUser" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_SelesaiKelola" targetRef="EndEvent_KelolaUser" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_PerbaikiDataUser" targetRef="Task_MasukkanDataUser" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_TampilkanFormUser" name="Tampilkan form kelola user">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidasiDataUser" name="Validasi data user">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_DataUserValid" name="Data user valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_OperasiUser" name="Jenis operasi?">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_TambahUser" name="Tambah user baru">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_EditUser" name="Edit data user">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_HapusUser" name="Hapus user">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S10</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CatatAuditUser" name="Catat log audit user (Include)">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:incoming>Flow_S9</bpmn:incoming>
      <bpmn:incoming>Flow_S10</bpmn:incoming>
      <bpmn:outgoing>Flow_S11</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanKonfirmasi" name="Tampilkan konfirmasi berhasil">
      <bpmn:incoming>Flow_S11</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanErrorUser" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_TampilkanFormUser" targetRef="Task_ValidasiDataUser" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidasiDataUser" targetRef="Gateway_DataUserValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Ya" sourceRef="Gateway_DataUserValid" targetRef="Gateway_OperasiUser" />
    <bpmn:sequenceFlow id="Flow_S4" name="Tidak" sourceRef="Gateway_DataUserValid" targetRef="Task_TampilkanErrorUser" />
    <bpmn:sequenceFlow id="Flow_S5" name="Tambah" sourceRef="Gateway_OperasiUser" targetRef="Task_TambahUser" />
    <bpmn:sequenceFlow id="Flow_S6" name="Edit" sourceRef="Gateway_OperasiUser" targetRef="Task_EditUser" />
    <bpmn:sequenceFlow id="Flow_S7" name="Hapus" sourceRef="Gateway_OperasiUser" targetRef="Task_HapusUser" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_TambahUser" targetRef="Task_CatatAuditUser" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_EditUser" targetRef="Task_CatatAuditUser" />
    <bpmn:sequenceFlow id="Flow_S10" sourceRef="Task_HapusUser" targetRef="Task_CatatAuditUser" />
    <bpmn:sequenceFlow id="Flow_S11" sourceRef="Task_CatatAuditUser" targetRef="Task_TampilkanKonfirmasi" />
  </bpmn:process>
</bpmn:definitions>
