<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_GenerateLaporanBulananAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_GenerateLaporanBulananAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_PilihBulan" targetRef="Task_ValidasiBulan" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanLaporan" targetRef="Task_LihatLaporan" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_DownloadLaporan" targetRef="Task_UnduhLaporan" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Proses Admin" isExecutable="false">
    <bpmn:startEvent id="StartEvent_GenerateLaporanBulanan" name="Mulai Generate Laporan Bulanan">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_BukaMenuLaporanBulanan" name="Buka menu laporan bulanan">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihBulan" name="Pilih bulan dan tahun">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_LihatLaporan" name="Lihat laporan bulanan">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_UnduhLaporan" name="Unduh laporan?">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_UnduhLaporan" name="Unduh laporan PDF">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_10</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_PilihBulanLain" name="Pilih bulan yang valid">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_GenerateLaporanBulanan" name="Selesai">
      <bpmn:incoming>Flow_9</bpmn:incoming>
      <bpmn:incoming>Flow_10</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_GenerateLaporanBulanan" targetRef="Task_BukaMenuLaporanBulanan" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_BukaMenuLaporanBulanan" targetRef="Task_PilihBulan" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_PilihBulan" targetRef="Task_LihatLaporan" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_LihatLaporan" targetRef="Gateway_UnduhLaporan" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_LihatLaporan" targetRef="Gateway_UnduhLaporan" />
    <bpmn:sequenceFlow id="Flow_6" name="Ya" sourceRef="Gateway_UnduhLaporan" targetRef="Task_UnduhLaporan" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_PilihBulanLain" targetRef="Task_PilihBulan" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_PilihBulanLain" targetRef="Task_PilihBulan" />
    <bpmn:sequenceFlow id="Flow_9" name="Tidak" sourceRef="Gateway_UnduhLaporan" targetRef="EndEvent_GenerateLaporanBulanan" />
    <bpmn:sequenceFlow id="Flow_10" sourceRef="Task_UnduhLaporan" targetRef="EndEvent_GenerateLaporanBulanan" />
  </bpmn:process>
  
  <bpmn:process id="Process_Sistem" name="Proses Sistem" isExecutable="false">
    <bpmn:serviceTask id="Task_TampilkanFormLaporanBulanan" name="Tampilkan form laporan bulanan">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidasiBulan" name="Validasi bulan dan tahun">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_BulanValid" name="Bulan valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_AmbilDataBulanan" name="Ambil data penjualan bulanan">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_HitungTotalBulanan" name="Hitung total penjualan bulanan">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_HitungProfit" name="Hitung profit bulanan">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_BuatGrafik" name="Buat grafik penjualan">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatLaporanBulanan" name="Format laporan bulanan">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanLaporan" name="Tampilkan laporan bulanan">
      <bpmn:incoming>Flow_S9</bpmn:incoming>
      <bpmn:outgoing>Flow_S10</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_GeneratePDF" name="Generate PDF laporan">
      <bpmn:incoming>Flow_S10</bpmn:incoming>
      <bpmn:outgoing>Flow_S11</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_DownloadLaporan" name="Download laporan PDF">
      <bpmn:incoming>Flow_S11</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_TampilkanError" name="Tampilkan pesan kesalahan">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_TampilkanFormLaporanBulanan" targetRef="Task_ValidasiBulan" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidasiBulan" targetRef="Gateway_BulanValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Ya" sourceRef="Gateway_BulanValid" targetRef="Task_AmbilDataBulanan" />
    <bpmn:sequenceFlow id="Flow_S4" name="Tidak" sourceRef="Gateway_BulanValid" targetRef="Task_TampilkanError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_AmbilDataBulanan" targetRef="Task_HitungTotalBulanan" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_HitungTotalBulanan" targetRef="Task_HitungProfit" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_HitungProfit" targetRef="Task_BuatGrafik" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_BuatGrafik" targetRef="Task_FormatLaporanBulanan" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_FormatLaporanBulanan" targetRef="Task_TampilkanLaporan" />
    <bpmn:sequenceFlow id="Flow_S10" sourceRef="Task_TampilkanLaporan" targetRef="Task_GeneratePDF" />
    <bpmn:sequenceFlow id="Flow_S11" sourceRef="Task_GeneratePDF" targetRef="Task_DownloadLaporan" />
  </bpmn:process>
</bpmn:definitions>
