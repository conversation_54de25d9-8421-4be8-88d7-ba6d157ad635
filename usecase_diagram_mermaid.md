# Use Case Diagram - Sistem Ubi Bakar Cilembu (Mermaid Version)

```mermaid
graph TB
    %% Actor definitions
    Karyawan[👤 Karyawan]
    Admin[👤 Admin]
    
    %% System boundary
    subgraph "Sistem Ubi Bakar Cilembu"
        %% Top row use cases
        UC01((Login<br/>Sistem))
        UC02((Logout<br/>Sistem))
        
        %% Second row use cases
        UC03((Kelola Stok<br/>Ubi Mentah))
        UC04((Kelola Stok<br/>Ubi Matang))
        
        %% Third row use cases
        UC05((Lihat Stok<br/>Real-time))
        UC06((Catat Penjualan<br/>Harian))
        
        %% Fourth row use cases
        UC07((Proses<br/>Transaksi))
        UC08((Generate Laporan<br/>Hari<PERSON>))
        
        %% Fifth row use cases
        UC09((Generate Laporan<br/>Bulanan))
        UC10((Tampilkan<br/>Profit))
        
        %% Bottom row use cases
        UC11((Kelola<br/>User))
        UC12((Audit<br/>Trail))
    end
    
    %% Left side connections (Ka<PERSON>wan)
    Karyawan --> UC01
    Karyawan --> UC02
    Karyawan --> UC04
    Karyawan --> UC05
    Karyawan --> UC06
    Karyawan --> UC07
    Karyawan --> UC08
    
    %% Right side connections (Admin)
    Admin --> UC01
    Admin --> UC02
    Admin --> UC03
    Admin --> UC04
    Admin --> UC05
    Admin --> UC06
    Admin --> UC07
    Admin --> UC08
    Admin --> UC09
    Admin --> UC10
    Admin --> UC11
    Admin --> UC12
    
    %% Include relationships
    UC01 -.->|include| UC12
    UC02 -.->|include| UC12
    UC06 -.->|include| UC07
    UC08 -.->|include| UC09
    UC09 -.->|include| UC10
    UC11 -.->|include| UC12
    
    %% Extend relationships
    UC05 -.->|extend| UC03
    UC05 -.->|extend| UC04
    UC10 -.->|extend| UC09
    
    %% Styling
    classDef actor fill:#F5E6D3,stroke:#8B4513,stroke-width:2px,color:#000
    classDef usecase fill:#E6F3FF,stroke:#4682B4,stroke-width:2px,color:#000
    classDef system fill:#FFFFFF,stroke:#000000,stroke-width:2px
    
    class Karyawan,Admin actor
    class UC01,UC02,UC03,UC04,UC05,UC06,UC07,UC08,UC09,UC10,UC11,UC12 usecase
```

## 🎯 Konversi PlantUML ke Mermaid

### **📐 Layout Features:**
- **Aktor Bersebrangan**: Karyawan di kiri, Admin di kanan
- **Use Cases Centered**: 12 use cases dalam grid 6x2
- **Garis Lurus**: Semua koneksi menggunakan garis lurus
- **System Boundary**: Subgraph untuk boundary sistem

### **👥 Role Access:**

#### **👨‍💼 KARYAWAN (7 Use Cases):**
- Login Sistem
- Logout Sistem  
- Kelola Stok Ubi Matang
- Lihat Stok Real-time
- Catat Penjualan Harian
- Proses Transaksi
- Generate Laporan Harian

#### **👨‍💻 ADMIN (12 Use Cases - Full Access):**
- Semua fitur Karyawan +
- Kelola Stok Ubi Mentah
- Generate Laporan Bulanan
- Tampilkan Profit
- Kelola User
- Audit Trail

### **🔗 Relationships:**

#### **Include (Mandatory):**
- Login/Logout → Audit Trail
- Catat Penjualan → Proses Transaksi
- Laporan Harian → Laporan Bulanan
- Laporan Bulanan → Tampilkan Profit
- Kelola User → Audit Trail

#### **Extend (Optional):**
- Lihat Stok Real-time → Kelola Stok Mentah/Matang
- Tampilkan Profit → Laporan Bulanan

### **🎨 Styling:**
- **Actors**: Warna coklat (#F5E6D3) dengan border #8B4513
- **Use Cases**: Warna biru muda (#E6F3FF) dengan border #4682B4
- **System Boundary**: Putih dengan border hitam
- **Relationships**: Garis putus-putus untuk include/extend
