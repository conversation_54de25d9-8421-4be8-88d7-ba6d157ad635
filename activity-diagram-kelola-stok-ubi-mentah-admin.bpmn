<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_ManageRawPotatoStockAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_ManageRawPotatoStockAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_EnterStockData" targetRef="Task_ValidateInput" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowConfirmation" targetRef="Task_ManagementComplete" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Admin Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_ManageRawStock" name="Start Manage Raw Stock">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenRawStockMenu" name="Open raw potato stock menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectOperation" name="Select operation (add/reduce/edit)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_EnterStockData" name="Enter raw potato stock data">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ManagementComplete" name="Stock management complete">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_CorrectData" name="Correct incorrect data">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_ManageRawStock" name="End">
      <bpmn:incoming>Flow_7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_ManageRawStock" targetRef="Task_OpenRawStockMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenRawStockMenu" targetRef="Task_SelectOperation" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SelectOperation" targetRef="Task_EnterStockData" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_EnterStockData" targetRef="Task_ManagementComplete" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_ManagementComplete" targetRef="EndEvent_ManageRawStock" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_CorrectData" targetRef="Task_EnterStockData" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_ManagementComplete" targetRef="EndEvent_ManageRawStock" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_CorrectData" targetRef="Task_EnterStockData" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowRawStockForm" name="Show raw stock management form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateInput" name="Validate input data">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_InputValid" name="Input valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_UpdateRawStock" name="Update raw potato stock">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CalculateCookedStock" name="Calculate estimated cooked stock">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_UpdateCookedStock" name="Update cooked potato stock">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_LogAudit" name="Log audit trail (Include)">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowConfirmation" name="Show success confirmation">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowError" name="Show error message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowRawStockForm" targetRef="Task_ValidateInput" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateInput" targetRef="Gateway_InputValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_InputValid" targetRef="Task_UpdateRawStock" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_InputValid" targetRef="Task_ShowError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_UpdateRawStock" targetRef="Task_CalculateCookedStock" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_CalculateCookedStock" targetRef="Task_UpdateCookedStock" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_UpdateCookedStock" targetRef="Task_LogAudit" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_LogAudit" targetRef="Task_ShowConfirmation" />
  </bpmn:process>
</bpmn:definitions>
