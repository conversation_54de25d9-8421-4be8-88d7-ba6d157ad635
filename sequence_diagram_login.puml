@startuml Sequence_Login_Sistem
title Sequence Diagram - Login Sistem (Include Audit Trail)

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor User
participant ":LoginPage" as LP
participant ":AuthController" as AC
participant ":AuditController" as AuC
participant ":Database" as DB

User -> LP: 1. accessLoginPage()
activate LP

LP -> User: 2. displayLoginForm()

User -> LP: 3. inputCredentials(username, password)

LP -> AC: 4. authenticateUser(username, password)
activate AC

AC -> DB: 4.1. validateCredentials(username, password)
activate DB
DB --> AC: 4.2. credentialResult()
deactivate DB

alt validCredentials
    AC -> DB: 4.3. getUserRole()
    activate DB
    DB --> AC: 4.4. userRole()
    deactivate DB

    AC -> AuC: 4.5. logLoginActivity(userId)
    activate AuC
    note right: Include relationship

    AuC -> DB: 4.6. saveAuditLog(loginEvent)
    activate DB
    DB --> AuC: 4.7. auditSaved()
    deactivate DB

    AuC --> AC: 4.8. auditCompleted()
    deactivate AuC

    AC --> LP: 4.9. loginSuccess(userRole)
    LP --> User: 4.10. redirectToDashboard()
else invalidCredentials
    AC -> AuC: 4.11. logFailedLogin(username)
    activate AuC

    AuC -> DB: 4.12. saveFailedAttempt()
    activate DB
    DB --> AuC: 4.13. attemptLogged()
    deactivate DB

    AuC --> AC: 4.14. auditCompleted()
    deactivate AuC

    AC --> LP: 4.15. loginFailed()
    LP --> User: 4.16. showErrorMessage()
end

deactivate AC
deactivate LP

@enduml
