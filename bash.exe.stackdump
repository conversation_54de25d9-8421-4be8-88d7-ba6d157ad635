Stack trace:
Frame         Function      Args
0007FFFFA790  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9690) msys-2.0.dll+0x2118E
0007FFFFA790  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFA790  0002100469F2 (00021028DF99, 0007FFFFA648, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA790  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA790  00021006A545 (0007FFFFA7A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFA7A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBBF690000 ntdll.dll
7FFBBD440000 KERNEL32.DLL
7FFBBCAB0000 KERNELBASE.dll
7FFBBE460000 USER32.dll
7FFBBC780000 win32u.dll
7FFBBEFF0000 GDI32.dll
7FFBBC830000 gdi32full.dll
7FFBBD110000 msvcp_win.dll
7FFBBC960000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBBD510000 advapi32.dll
7FFBBD320000 msvcrt.dll
7FFBBD970000 sechost.dll
7FFBBCA80000 bcrypt.dll
7FFBBED60000 RPCRT4.dll
7FFBBBF10000 CRYPTBASE.DLL
7FFBBCF50000 bcryptPrimitives.dll
7FFBBEB10000 IMM32.DLL
