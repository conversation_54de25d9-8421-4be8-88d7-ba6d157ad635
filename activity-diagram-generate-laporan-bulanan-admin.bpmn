<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_GenerateMonthlyReportAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_GenerateMonthlyReportAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_SelectMonth" targetRef="Task_ValidateMonth" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowReport" targetRef="Task_ViewReport" />
    <bpmn:messageFlow id="MessageFlow_3" sourceRef="Task_DownloadReport" targetRef="Task_DownloadPDF" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Admin Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_GenerateMonthlyReport" name="Start Generate Monthly Report">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenMonthlyReportMenu" name="Open monthly report menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectMonth" name="Select month and year">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ViewReport" name="View monthly report">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_DownloadReport" name="Download report?">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_DownloadPDF" name="Download PDF report">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_10</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectValidMonth" name="Select valid month">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_GenerateMonthlyReport" name="End">
      <bpmn:incoming>Flow_9</bpmn:incoming>
      <bpmn:incoming>Flow_10</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_GenerateMonthlyReport" targetRef="Task_OpenMonthlyReportMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenMonthlyReportMenu" targetRef="Task_SelectMonth" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SelectMonth" targetRef="Task_ViewReport" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_ViewReport" targetRef="Gateway_DownloadReport" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_ViewReport" targetRef="Gateway_DownloadReport" />
    <bpmn:sequenceFlow id="Flow_6" name="Yes" sourceRef="Gateway_DownloadReport" targetRef="Task_DownloadPDF" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_SelectValidMonth" targetRef="Task_SelectMonth" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_SelectValidMonth" targetRef="Task_SelectMonth" />
    <bpmn:sequenceFlow id="Flow_9" name="No" sourceRef="Gateway_DownloadReport" targetRef="EndEvent_GenerateMonthlyReport" />
    <bpmn:sequenceFlow id="Flow_10" sourceRef="Task_DownloadPDF" targetRef="EndEvent_GenerateMonthlyReport" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowMonthlyReportForm" name="Show monthly report form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateMonth" name="Validate month and year">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_MonthValid" name="Month valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_GetMonthlyData" name="Get monthly sales data">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CalculateMonthlyTotal" name="Calculate monthly total sales">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CalculateProfit" name="Calculate monthly profit">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CreateChart" name="Create sales chart">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatMonthlyReport" name="Format monthly report">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowReport" name="Show monthly report">
      <bpmn:incoming>Flow_S9</bpmn:incoming>
      <bpmn:outgoing>Flow_S10</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_GeneratePDF" name="Generate PDF report">
      <bpmn:incoming>Flow_S10</bpmn:incoming>
      <bpmn:outgoing>Flow_S11</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_DownloadReport" name="Download PDF report">
      <bpmn:incoming>Flow_S11</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowError" name="Show error message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowMonthlyReportForm" targetRef="Task_ValidateMonth" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateMonth" targetRef="Gateway_MonthValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_MonthValid" targetRef="Task_GetMonthlyData" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_MonthValid" targetRef="Task_ShowError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_GetMonthlyData" targetRef="Task_CalculateMonthlyTotal" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_CalculateMonthlyTotal" targetRef="Task_CalculateProfit" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_CalculateProfit" targetRef="Task_CreateChart" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_CreateChart" targetRef="Task_FormatMonthlyReport" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_FormatMonthlyReport" targetRef="Task_ShowReport" />
    <bpmn:sequenceFlow id="Flow_S10" sourceRef="Task_ShowReport" targetRef="Task_GeneratePDF" />
    <bpmn:sequenceFlow id="Flow_S11" sourceRef="Task_GeneratePDF" targetRef="Task_DownloadReport" />
  </bpmn:process>
</bpmn:definitions>
