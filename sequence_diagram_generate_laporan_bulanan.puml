@startuml Sequence_Generate_Laporan_Bulanan
title Sequence Diagram - Generate La<PERSON><PERSON> (Admin Only, Include <PERSON><PERSON><PERSON>, Extend Tam<PERSON>lkan Profit)

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor Admin
participant ":AdminReportPage" as ARP
participant ":AdminController" as AC
participant ":ReportController" as RC
participant ":ProfitController" as PrC
participant ":Database" as DB
participant ":ReportAggregator" as RA

Admin -> ARP: 1. selectMonth<PERSON>ear()
activate ARP

ARP -> AC: 2. checkAdminRole()
activate AC

AC -> DB: 2.1. validateRole()
activate DB
DB --> AC: 2.2. roleConfirmed()
deactivate DB

alt notAdmin
    AC --> ARP: 2.3. accessDenied()
    ARP --> Admin: 2.4. showAccessError()
else isAdmin
    AC -> RC: 2.5. generateMonthlyReport()
    activate RC
    note right: Include from daily reports
    
    RC -> DB: 2.6. fetchDailyReports()
    activate DB
    DB --> RC: 2.7. dailyReportsData()
    deactivate DB
    
    RC -> RA: 2.8. aggregateMonthlyData()
    activate RA
    
    RA -> DB: 2.9. calculateMonthlyTotals()
    activate DB
    DB --> RA: 2.10. monthlyTotals()
    deactivate DB
    
    RA --> RC: 2.11. monthlyReportCompiled()
    deactivate RA
    
    alt showProfitRequested
        RC -> PrC: 2.12. calculateProfit()
        activate PrC
        note right: Extend relationship
        
        PrC -> DB: 2.13. getProfitData()
        activate DB
        DB --> PrC: 2.14. profitCalculated()
        deactivate DB
        
        PrC --> RC: 2.15. profitDataReady()
        deactivate PrC
        
        RC --> AC: 2.16. monthlyReportWithProfit()
        AC --> ARP: 2.17. displayReportWithProfit()
        ARP --> Admin: 2.18. showMonthlyReportWithProfit()
    else normalReport
        RC --> AC: 2.19. monthlyReportReady()
        AC --> ARP: 2.20. displayMonthlyReport()
        ARP --> Admin: 2.21. showMonthlyReport()
    end
    
    deactivate RC
end

deactivate AC
deactivate ARP

@enduml
