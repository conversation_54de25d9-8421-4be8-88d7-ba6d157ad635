<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
  <bpmn2:collaboration id="Collaboration_1">
    <bpmn2:participant id="Participant_Pengguna" name="Pengguna" processRef="Process_Pengguna" />
    <bpmn2:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn2:messageFlow id="MessageFlow_1" sourceRef="Task_MasukkanKredensial" targetRef="Task_ValidasiLogin" />
    <bpmn2:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanDashboard" targetRef="Task_GunakanSistem" />
  </bpmn2:collaboration>
  
  <bpmn2:process id="Process_Pengguna" isExecutable="false">
    <bpmn2:startEvent id="StartEvent_1" name="Mulai Login">
      <bpmn2:outgoing>SequenceFlow_1</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:task id="Task_BukaHalamanLogin" name="Buka halaman login">
      <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_MasukkanKredensial" name="Masukkan username dan password">
      <bpmn2:incoming>SequenceFlow_2</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_3</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_GunakanSistem" name="Gunakan sistem">
      <bpmn2:incoming>SequenceFlow_3</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_4</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:endEvent id="EndEvent_1" name="Selesai">
      <bpmn2:incoming>SequenceFlow_4</bpmn2:incoming>
    </bpmn2:endEvent>
    <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="Task_BukaHalamanLogin" />
    <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="Task_BukaHalamanLogin" targetRef="Task_MasukkanKredensial" />
    <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="Task_MasukkanKredensial" targetRef="Task_GunakanSistem" />
    <bpmn2:sequenceFlow id="SequenceFlow_4" sourceRef="Task_GunakanSistem" targetRef="EndEvent_1" />
  </bpmn2:process>
  
  <bpmn2:process id="Process_Sistem" isExecutable="false">
    <bpmn2:task id="Task_ValidasiLogin" name="Validasi kredensial">
      <bpmn2:outgoing>SequenceFlow_S1</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:exclusiveGateway id="Gateway_1" name="Valid?">
      <bpmn2:incoming>SequenceFlow_S1</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S2</bpmn2:outgoing>
      <bpmn2:outgoing>SequenceFlow_S3</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:task id="Task_CatatAuditBerhasil" name="Catat log audit berhasil (Include)">
      <bpmn2:incoming>SequenceFlow_S2</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S4</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_TampilkanDashboard" name="Tampilkan dashboard">
      <bpmn2:incoming>SequenceFlow_S4</bpmn2:incoming>
    </bpmn2:task>
    <bpmn2:task id="Task_TampilkanError" name="Tampilkan error">
      <bpmn2:incoming>SequenceFlow_S3</bpmn2:incoming>
    </bpmn2:task>
    <bpmn2:sequenceFlow id="SequenceFlow_S1" sourceRef="Task_ValidasiLogin" targetRef="Gateway_1" />
    <bpmn2:sequenceFlow id="SequenceFlow_S2" name="Ya" sourceRef="Gateway_1" targetRef="Task_CatatAuditBerhasil" />
    <bpmn2:sequenceFlow id="SequenceFlow_S3" name="Tidak" sourceRef="Gateway_1" targetRef="Task_TampilkanError" />
    <bpmn2:sequenceFlow id="SequenceFlow_S4" sourceRef="Task_CatatAuditBerhasil" targetRef="Task_TampilkanDashboard" />
  </bpmn2:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1">
      <bpmndi:BPMNShape id="Participant_Pengguna_di" bpmnElement="Participant_Pengguna" isHorizontal="true">
        <dc:Bounds x="160" y="80" width="800" height="200" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_Sistem_di" bpmnElement="Participant_Sistem" isHorizontal="true">
        <dc:Bounds x="160" y="320" width="800" height="200" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="212" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="195" y="205" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_BukaHalamanLogin_di" bpmnElement="Task_BukaHalamanLogin">
        <dc:Bounds x="300" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_MasukkanKredensial_di" bpmnElement="Task_MasukkanKredensial">
        <dc:Bounds x="450" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_GunakanSistem_di" bpmnElement="Task_GunakanSistem">
        <dc:Bounds x="600" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="752" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="750" y="205" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="Task_ValidasiLogin_di" bpmnElement="Task_ValidasiLogin">
        <dc:Bounds x="450" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1_di" bpmnElement="Gateway_1" isMarkerVisible="true">
        <dc:Bounds x="595" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="605" y="452" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_CatatAuditBerhasil_di" bpmnElement="Task_CatatAuditBerhasil">
        <dc:Bounds x="700" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_TampilkanDashboard_di" bpmnElement="Task_TampilkanDashboard">
        <dc:Bounds x="850" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_TampilkanError_di" bpmnElement="Task_TampilkanError">
        <dc:Bounds x="700" y="460" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNEdge id="SequenceFlow_1_di" bpmnElement="SequenceFlow_1">
        <di:waypoint x="248" y="180" />
        <di:waypoint x="300" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_2_di" bpmnElement="SequenceFlow_2">
        <di:waypoint x="400" y="180" />
        <di:waypoint x="450" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_3_di" bpmnElement="SequenceFlow_3">
        <di:waypoint x="550" y="180" />
        <di:waypoint x="600" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_4_di" bpmnElement="SequenceFlow_4">
        <di:waypoint x="700" y="180" />
        <di:waypoint x="752" y="180" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="SequenceFlow_S1_di" bpmnElement="SequenceFlow_S1">
        <di:waypoint x="550" y="420" />
        <di:waypoint x="595" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S2_di" bpmnElement="SequenceFlow_S2">
        <di:waypoint x="620" y="395" />
        <di:waypoint x="620" y="380" />
        <di:waypoint x="700" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="628" y="385" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S3_di" bpmnElement="SequenceFlow_S3">
        <di:waypoint x="620" y="445" />
        <di:waypoint x="620" y="500" />
        <di:waypoint x="700" y="500" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="622" y="470" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S4_di" bpmnElement="SequenceFlow_S4">
        <di:waypoint x="800" y="380" />
        <di:waypoint x="850" y="380" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="MessageFlow_1_di" bpmnElement="MessageFlow_1">
        <di:waypoint x="500" y="220" />
        <di:waypoint x="500" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="MessageFlow_2_di" bpmnElement="MessageFlow_2">
        <di:waypoint x="900" y="340" />
        <di:waypoint x="900" y="280" />
        <di:waypoint x="650" y="280" />
        <di:waypoint x="650" y="220" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>
