# 🧪 TEST CASE BLACK BOX TESTING
# SISTEM INFORMASI MANAJEMEN UBI BAKAR CILEMBU

## 📋 **INFORMASI DOKUMEN**

| **Atribut** | **Detail** |
|-------------|------------|
| **Nama Sistem** | Sistem Informasi Manajemen Ubi Bakar Cilembu |
| **Testing Method** | Black Box Testing |
| **Focus** | Input-Output Validation & Business Logic |
| **Tanggal Dibuat** | 27 Juli 2025 |
| **Total Test Case** | 100+ Test Cases |
| **Coverage** | Functional Testing, Boundary Testing, Error Handling |

---

## 🎯 **TUJUAN BLACK BOX TESTING**

Black Box Testing bertujuan untuk:
1. ✅ Menguji fungsionalitas tanpa melihat kode internal
2. ✅ Memvalidasi input-output sesuai spesifikasi
3. ✅ Menguji boundary conditions dan edge cases
4. ✅ Memverifikasi error handling dan validation
5. ✅ Mengonfirmasi business logic ber<PERSON><PERSON> benar

---

## 📊 **KATEGORI TESTING**

### **🔍 Equivalence Partitioning**
- Valid input classes
- Invalid input classes
- Boundary value analysis

### **🎯 Boundary Value Analysis**
- Minimum values
- Maximum values
- Just below/above boundaries

### **❌ Error Guessing**
- Common error scenarios
- Edge cases
- Negative testing

---

## 🔐 **MODUL 1: AUTHENTICATION - INPUT VALIDATION**

### **TC-BB-AUTH-001: Email Format Validation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-AUTH-001 |
| **Test Case Name** | Validasi Format Email |
| **Category** | Input Validation |
| **Priority** | High |

**Test Data & Expected Results:**

| **Input Email** | **Expected Result** | **Reason** |
|-----------------|-------------------|------------|
| `<EMAIL>` | ✅ VALID | Valid email format |
| `<EMAIL>` | ✅ VALID | Valid with subdomain |
| `<EMAIL>` | ✅ VALID | Valid with numbers |
| `invalid-email` | ❌ INVALID | Missing @ symbol |
| `@domain.com` | ❌ INVALID | Missing local part |
| `user@` | ❌ INVALID | Missing domain |
| `user@domain` | ❌ INVALID | Missing TLD |
| `<EMAIL>` | ❌ INVALID | Double dots |
| `<EMAIL>` | ❌ INVALID | Double dots in domain |
| `` (empty) | ❌ INVALID | Required field |

### **TC-BB-AUTH-002: Password Strength Validation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-AUTH-002 |
| **Test Case Name** | Validasi Kekuatan Password |
| **Category** | Input Validation |
| **Priority** | High |

**Test Data & Expected Results:**

| **Input Password** | **Expected Result** | **Reason** |
|-------------------|-------------------|------------|
| `Admin123!` | ✅ VALID | Strong password |
| `password123` | ✅ VALID | Meets minimum requirements |
| `12345678` | ✅ VALID | 8 characters minimum |
| `1234567` | ❌ INVALID | Less than 8 characters |
| `123` | ❌ INVALID | Too short |
| `` (empty) | ❌ INVALID | Required field |
| `   ` (spaces) | ❌ INVALID | Only whitespace |

### **TC-BB-AUTH-003: Login Attempt Limits**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-AUTH-003 |
| **Test Case Name** | Batas Percobaan Login |
| **Category** | Security Logic |
| **Priority** | High |

**Test Steps:**
1. Attempt login with wrong password 5 times
2. Check if account gets locked
3. Try login with correct password after lock

**Expected Results:**
- ✅ Account locked after 5 failed attempts
- ✅ Error message: "Account temporarily locked"
- ✅ Correct password rejected during lock period
- ✅ Account unlocked after timeout period

---

## 📦 **MODUL 2: INVENTORY - BUSINESS LOGIC**

### **TC-BB-INV-001: Stock Calculation Logic**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-INV-001 |
| **Test Case Name** | Logika Perhitungan Stok |
| **Category** | Business Logic |
| **Priority** | High |

**Test Scenario:**
- Initial Stock: 100 kg
- Add Stock: +50 kg
- Process Stock: -30 kg
- Expected Final Stock: 120 kg

**Test Data:**

| **Operation** | **Input** | **Expected Stock** | **Status** |
|---------------|-----------|-------------------|------------|
| Initial | 100 kg | 100 kg | ✅ |
| Add Stock | +50 kg | 150 kg | ✅ |
| Process | -30 kg | 120 kg | ✅ |
| Process | -150 kg | ERROR | ❌ Insufficient stock |

### **TC-BB-INV-002: Expiry Date Validation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-INV-002 |
| **Test Case Name** | Validasi Tanggal Kadaluarsa |
| **Category** | Date Validation |
| **Priority** | High |

**Test Data:**

| **Purchase Date** | **Expiry Date** | **Expected Result** | **Reason** |
|-------------------|-----------------|-------------------|------------|
| 2025-07-27 | 2025-08-27 | ✅ VALID | Future date |
| 2025-07-27 | 2025-07-26 | ❌ INVALID | Past date |
| 2025-07-27 | 2025-07-27 | ❌ INVALID | Same day |
| 2025-07-27 | 2026-07-27 | ✅ VALID | 1 year later |
| 2025-07-27 | `` (empty) | ✅ VALID | Optional field |

### **TC-BB-INV-003: Minimum Stock Threshold**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-INV-003 |
| **Test Case Name** | Ambang Batas Minimum Stok |
| **Category** | Alert Logic |
| **Priority** | Medium |

**Test Data:**

| **Current Stock** | **Min Threshold** | **Expected Alert** | **Alert Level** |
|-------------------|-------------------|-------------------|-----------------|
| 15 | 10 | ❌ NO ALERT | Normal |
| 10 | 10 | ⚠️ WARNING | At threshold |
| 8 | 10 | 🚨 CRITICAL | Below threshold |
| 0 | 10 | 🔴 OUT OF STOCK | Zero stock |
| 5 | 20 | 🚨 CRITICAL | Well below threshold |

---

## 💰 **MODUL 3: TRANSACTION - CALCULATION LOGIC**

### **TC-BB-TXN-001: Price Calculation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-TXN-001 |
| **Test Case Name** | Perhitungan Harga Transaksi |
| **Category** | Calculation Logic |
| **Priority** | High |

**Test Scenario:**
- Product A: Rp 8,000 x 3 = Rp 24,000
- Product B: Rp 12,000 x 2 = Rp 24,000
- Subtotal: Rp 48,000
- Tax (10%): Rp 4,800
- Discount: Rp 5,000
- Total: Rp 47,800

**Test Data:**

| **Item** | **Price** | **Qty** | **Subtotal** | **Expected** |
|----------|-----------|---------|--------------|--------------|
| Ubi Original | 8,000 | 3 | 24,000 | ✅ |
| Ubi Premium | 12,000 | 2 | 24,000 | ✅ |
| **TOTAL** | | | **48,000** | ✅ |
| Tax (10%) | | | 4,800 | ✅ |
| Discount | | | -5,000 | ✅ |
| **FINAL** | | | **47,800** | ✅ |

### **TC-BB-TXN-002: Change Calculation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-TXN-002 |
| **Test Case Name** | Perhitungan Kembalian |
| **Category** | Calculation Logic |
| **Priority** | High |

**Test Data:**

| **Total Amount** | **Amount Paid** | **Expected Change** | **Status** |
|------------------|-----------------|-------------------|------------|
| 25,000 | 30,000 | 5,000 | ✅ VALID |
| 25,000 | 25,000 | 0 | ✅ EXACT |
| 25,000 | 20,000 | ERROR | ❌ INSUFFICIENT |
| 25,000 | 100,000 | 75,000 | ✅ VALID |
| 25,000 | 0 | ERROR | ❌ INVALID |
| 25,000 | -5,000 | ERROR | ❌ NEGATIVE |

### **TC-BB-TXN-003: Stock Deduction Logic**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-TXN-003 |
| **Test Case Name** | Logika Pengurangan Stok |
| **Category** | Business Logic |
| **Priority** | High |

**Test Scenario:**
- Product Stock: 50 units
- Transaction Qty: 5 units
- Expected Remaining: 45 units

**Test Data:**

| **Initial Stock** | **Transaction Qty** | **Expected Remaining** | **Status** |
|-------------------|-------------------|----------------------|------------|
| 50 | 5 | 45 | ✅ VALID |
| 50 | 50 | 0 | ✅ VALID |
| 50 | 51 | ERROR | ❌ INSUFFICIENT |
| 0 | 1 | ERROR | ❌ OUT OF STOCK |
| 10 | 0 | 10 | ✅ NO CHANGE |

---

## 💳 **MODUL 4: PAYMENT GATEWAY - INTEGRATION LOGIC**

### **TC-BB-PAY-001: Payment Amount Validation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-PAY-001 |
| **Test Case Name** | Validasi Jumlah Pembayaran |
| **Category** | Input Validation |
| **Priority** | High |

**Test Data:**

| **Transaction Amount** | **Payment Amount** | **Expected Result** | **Reason** |
|------------------------|-------------------|-------------------|------------|
| 25,000 | 25,000 | ✅ VALID | Exact match |
| 25,000 | 25,001 | ❌ INVALID | Amount mismatch |
| 25,000 | 24,999 | ❌ INVALID | Amount mismatch |
| 25,000 | 0 | ❌ INVALID | Zero amount |
| 25,000 | -1,000 | ❌ INVALID | Negative amount |
| 25,000 | 1,000,000 | ❌ INVALID | Exceeds limit |

### **TC-BB-PAY-002: Payment Method Validation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-PAY-002 |
| **Test Case Name** | Validasi Metode Pembayaran |
| **Category** | Input Validation |
| **Priority** | Medium |

**Test Data:**

| **Payment Method** | **Expected Result** | **Available** |
|-------------------|-------------------|---------------|
| `cash` | ✅ VALID | Always |
| `credit_card` | ✅ VALID | Via Midtrans |
| `bank_transfer` | ✅ VALID | Via Midtrans |
| `gopay` | ✅ VALID | Via Midtrans |
| `ovo` | ✅ VALID | Via Midtrans |
| `invalid_method` | ❌ INVALID | Not supported |
| `` (empty) | ❌ INVALID | Required field |

### **TC-BB-PAY-003: Payment Status Handling**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-PAY-003 |
| **Test Case Name** | Handling Status Pembayaran |
| **Category** | Status Logic |
| **Priority** | High |

**Test Data:**

| **Payment Status** | **Transaction Status** | **Stock Action** | **Expected** |
|-------------------|----------------------|------------------|--------------|
| `settlement` | `completed` | Deduct stock | ✅ VALID |
| `pending` | `pending` | Hold stock | ✅ VALID |
| `cancel` | `cancelled` | Release stock | ✅ VALID |
| `expire` | `expired` | Release stock | ✅ VALID |
| `deny` | `failed` | Release stock | ✅ VALID |

---

## 🏭 **MODUL 5: PRODUCTION - CONVERSION LOGIC**

### **TC-BB-PROD-001: Raw to Processed Conversion**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-PROD-001 |
| **Test Case Name** | Konversi Ubi Mentah ke Ubi Bakar |
| **Category** | Conversion Logic |
| **Priority** | High |

**Test Data:**

| **Raw Input (kg)** | **Expected Output (pcs)** | **Conversion Rate** | **Status** |
|-------------------|---------------------------|-------------------|------------|
| 10 | 40 | 1:4 | ✅ VALID |
| 25 | 100 | 1:4 | ✅ VALID |
| 0 | 0 | N/A | ❌ INVALID |
| -5 | ERROR | N/A | ❌ NEGATIVE |
| 1000 | 4000 | 1:4 | ⚠️ CHECK CAPACITY |

### **TC-BB-PROD-002: Cost Calculation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-PROD-002 |
| **Test Case Name** | Perhitungan Biaya Produksi |
| **Category** | Cost Logic |
| **Priority** | High |

**Test Scenario:**
- Raw Material: 10 kg @ Rp 15,000/kg = Rp 150,000
- Labor Cost: Rp 50,000
- Utility Cost: Rp 25,000
- Total Cost: Rp 225,000
- Output: 40 pieces
- Cost per Unit: Rp 5,625

**Test Data:**

| **Component** | **Amount** | **Expected** |
|---------------|------------|--------------|
| Raw Material | 150,000 | ✅ |
| Labor | 50,000 | ✅ |
| Utility | 25,000 | ✅ |
| **Total Cost** | **225,000** | ✅ |
| **Cost/Unit** | **5,625** | ✅ |

### **TC-BB-PROD-003: Quality Grade Assignment**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-PROD-003 |
| **Test Case Name** | Penentuan Grade Kualitas |
| **Category** | Quality Logic |
| **Priority** | Medium |

**Test Data:**

| **Quality Score** | **Expected Grade** | **Price Multiplier** |
|-------------------|-------------------|-------------------|
| 90-100 | A | 1.0x |
| 80-89 | B | 0.8x |
| 70-79 | C | 0.6x |
| < 70 | REJECT | 0x |

---

## 🚚 **MODUL 6: DISTRIBUTION - ALLOCATION LOGIC**

### **TC-BB-DIST-001: Product Allocation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-DIST-001 |
| **Test Case Name** | Alokasi Produk untuk Distribusi |
| **Category** | Allocation Logic |
| **Priority** | High |

**Test Data:**

| **Available Stock** | **Distribution Qty** | **Expected Result** | **Status** |
|-------------------|---------------------|-------------------|------------|
| 100 | 50 | ✅ ALLOCATED | Valid |
| 100 | 100 | ✅ ALLOCATED | Full allocation |
| 100 | 101 | ❌ ERROR | Insufficient stock |
| 0 | 10 | ❌ ERROR | No stock |
| 50 | 0 | ⚠️ WARNING | Zero allocation |

### **TC-BB-DIST-002: Market Distance Calculation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-DIST-002 |
| **Test Case Name** | Perhitungan Jarak ke Pasar |
| **Category** | Distance Logic |
| **Priority** | Medium |

**Test Data:**

| **Market** | **Distance (km)** | **Delivery Cost** | **Expected** |
|------------|-------------------|-------------------|--------------|
| Pasar Baru | 5 | 25,000 | ✅ VALID |
| Pasar Cicadas | 15 | 50,000 | ✅ VALID |
| Pasar Kosambi | 25 | 75,000 | ✅ VALID |
| Invalid Market | N/A | ERROR | ❌ INVALID |

---

## 📊 **MODUL 7: REPORTS - DATA AGGREGATION**

### **TC-BB-REP-001: Sales Report Calculation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-REP-001 |
| **Test Case Name** | Perhitungan Laporan Penjualan |
| **Category** | Aggregation Logic |
| **Priority** | High |

**Test Data:**
- Transaction 1: Rp 25,000 (3 items)
- Transaction 2: Rp 40,000 (5 items)
- Transaction 3: Rp 15,000 (2 items)

**Expected Results:**

| **Metric** | **Expected Value** | **Calculation** |
|------------|-------------------|-----------------|
| Total Sales | Rp 80,000 | Sum of all transactions |
| Total Items | 10 | Sum of all quantities |
| Avg Transaction | Rp 26,667 | Total / Count |
| Transaction Count | 3 | Count of transactions |

### **TC-BB-REP-002: Date Range Filtering**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-REP-002 |
| **Test Case Name** | Filter Berdasarkan Rentang Tanggal |
| **Category** | Filter Logic |
| **Priority** | High |

**Test Data:**

| **Start Date** | **End Date** | **Expected Result** | **Status** |
|----------------|--------------|-------------------|------------|
| 2025-07-01 | 2025-07-31 | ✅ VALID | Valid range |
| 2025-07-31 | 2025-07-01 | ❌ INVALID | End < Start |
| 2025-07-01 | `` (empty) | ❌ INVALID | Missing end date |
| `` (empty) | 2025-07-31 | ❌ INVALID | Missing start date |
| 2025-07-01 | 2025-07-01 | ✅ VALID | Same day |

### **TC-BB-REP-003: Profit Margin Calculation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-REP-003 |
| **Test Case Name** | Perhitungan Margin Keuntungan |
| **Category** | Financial Logic |
| **Priority** | High |

**Test Data:**

| **Selling Price** | **Cost Price** | **Expected Margin** | **Margin %** |
|-------------------|----------------|-------------------|--------------|
| 10,000 | 6,000 | 4,000 | 40% |
| 8,000 | 5,000 | 3,000 | 37.5% |
| 5,000 | 5,000 | 0 | 0% |
| 5,000 | 6,000 | -1,000 | -20% (Loss) |

---

## 🔍 **MODUL 8: SEARCH & FILTER - QUERY LOGIC**

### **TC-BB-SEARCH-001: Product Search**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-SEARCH-001 |
| **Test Case Name** | Pencarian Produk |
| **Category** | Search Logic |
| **Priority** | Medium |

**Test Data:**

| **Search Query** | **Expected Results** | **Match Type** |
|------------------|-------------------|----------------|
| `Ubi Original` | Products with "Ubi Original" | Exact match |
| `ubi` | All ubi products | Partial match |
| `UBI` | All ubi products | Case insensitive |
| `123` | Products with "123" in name/code | Alphanumeric |
| `` (empty) | All products | No filter |
| `xyz999` | No results | No match |

### **TC-BB-SEARCH-002: Date Range Search**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-SEARCH-002 |
| **Test Case Name** | Pencarian Berdasarkan Tanggal |
| **Category** | Date Filter |
| **Priority** | Medium |

**Test Data:**

| **Date Range** | **Expected Behavior** | **Status** |
|----------------|---------------------|------------|
| Today | Records from today only | ✅ VALID |
| This Week | Records from current week | ✅ VALID |
| This Month | Records from current month | ✅ VALID |
| Custom Range | Records within specified range | ✅ VALID |
| Invalid Range | Error message | ❌ INVALID |

---

## 📱 **MODUL 9: MOBILE RESPONSIVENESS - UI LOGIC**

### **TC-BB-MOB-001: Screen Size Adaptation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-MOB-001 |
| **Test Case Name** | Adaptasi Ukuran Layar |
| **Category** | Responsive Design |
| **Priority** | Medium |

**Test Data:**

| **Screen Size** | **Expected Layout** | **Navigation** |
|-----------------|-------------------|----------------|
| Desktop (1920x1080) | Full layout | Horizontal menu |
| Tablet (768x1024) | Responsive layout | Collapsible menu |
| Mobile (375x667) | Mobile layout | Hamburger menu |
| Small Mobile (320x568) | Compact layout | Minimal menu |

### **TC-BB-MOB-002: Touch Interface**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-MOB-002 |
| **Test Case Name** | Interface Sentuh |
| **Category** | Touch Interaction |
| **Priority** | Medium |

**Test Data:**

| **Element** | **Touch Target Size** | **Expected** |
|-------------|---------------------|--------------|
| Buttons | ≥ 44px | ✅ ACCESSIBLE |
| Links | ≥ 44px | ✅ ACCESSIBLE |
| Form inputs | ≥ 44px height | ✅ ACCESSIBLE |
| Small icons | < 44px | ❌ TOO SMALL |

---

## 🔒 **MODUL 10: SECURITY - VALIDATION LOGIC**

### **TC-BB-SEC-001: Input Sanitization**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-SEC-001 |
| **Test Case Name** | Sanitasi Input |
| **Category** | Security Validation |
| **Priority** | High |

**Test Data:**

| **Input** | **Expected Output** | **Security** |
|-----------|-------------------|--------------|
| `<script>alert('xss')</script>` | Escaped/Stripped | ✅ XSS Prevention |
| `'; DROP TABLE users; --` | Escaped | ✅ SQL Injection Prevention |
| `../../../etc/passwd` | Blocked | ✅ Path Traversal Prevention |
| `Normal text` | Normal text | ✅ Valid input |

### **TC-BB-SEC-002: File Upload Validation**
| **Field** | **Value** |
|-----------|-----------|
| **Test Case ID** | TC-BB-SEC-002 |
| **Test Case Name** | Validasi Upload File |
| **Category** | File Security |
| **Priority** | High |

**Test Data:**

| **File Type** | **Size** | **Expected Result** | **Reason** |
|---------------|----------|-------------------|------------|
| image.jpg | 1MB | ✅ ACCEPTED | Valid image |
| image.png | 2MB | ✅ ACCEPTED | Valid image |
| document.pdf | 5MB | ❌ REJECTED | Not image |
| script.php | 1KB | ❌ REJECTED | Executable file |
| image.jpg | 10MB | ❌ REJECTED | Too large |

---

## 📈 **KRITERIA PASS/FAIL BLACK BOX TESTING**

### **✅ PASS CRITERIA**
- **Input Validation**: 100% validation rules working
- **Calculation Logic**: All calculations accurate
- **Business Rules**: All business logic correct
- **Error Handling**: Proper error messages
- **Boundary Values**: All boundaries handled correctly

### **❌ FAIL CRITERIA**
- Incorrect calculation results
- Invalid input accepted
- Business rules violated
- Poor error handling
- Security vulnerabilities

---

## 📊 **TEST EXECUTION MATRIX**

### **Priority Levels**
- **🔴 High**: Critical business functions
- **🟡 Medium**: Important features
- **🟢 Low**: Nice-to-have features

### **Test Categories**
- **Functional**: Core business logic
- **Validation**: Input/output validation
- **Calculation**: Mathematical operations
- **Security**: Security measures
- **Integration**: System integration

---

## 📋 **DEFECT CLASSIFICATION**

### **Severity Levels**
- **Critical**: System crash, data loss
- **High**: Major function not working
- **Medium**: Minor function issues
- **Low**: Cosmetic issues

### **Priority Levels**
- **P1**: Fix immediately
- **P2**: Fix in current release
- **P3**: Fix in next release
- **P4**: Fix when time permits

---

**📅 Document Version**: 1.0  
**👨‍💻 Created By**: QA Team  
**📧 Contact**: <EMAIL>  
**🔄 Last Updated**: 27 Juli 2025
