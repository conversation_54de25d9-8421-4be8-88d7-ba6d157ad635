@startuml Sequence_Kelola_Stok_Ubi_Matang
title Sequence Diagram - <PERSON><PERSON><PERSON> (Include Lihat Stok Real-time)

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor User
participant ":StokUbiMatangPage" as SMP
participant ":StokController" as SC
participant ":StokRealTimeController" as SRC
participant ":Database" as DB

User -> SMP: 1. accessStokUbiMatangPage()
activate SMP

SMP -> SC: 2. loadStokUbiMatang()
activate SC

SC -> SRC: 2.1. getRealTimeStok()
activate SRC
note right: Include relationship

SRC -> DB: 2.2. getCurrentStokUbiMatang()
activate DB
DB --> SRC: 2.3. realTimeStokData()
deactivate DB

SRC --> SC: 2.4. stokRealTimeReady()
deactivate SRC

SC --> SMP: 2.5. displayStokData()
SMP --> User: 2.6. showStokUbiMatang()

User -> SMP: 3. updateStok(jumlahBaru)

SMP -> SC: 4. updateStokUbiMatang(jumlahBaru)

SC -> DB: 4.1. validateStokUpdate(jumlahBaru)
activate DB
DB --> SC: 4.2. validationResult()
deactivate DB

alt validUpdate
    SC -> DB: 4.3. updateStokUbiMatang(jumlahBaru)
    activate DB
    DB --> SC: 4.4. stokUpdated()
    deactivate DB
    
    SC -> SRC: 4.5. refreshRealTimeStok()
    activate SRC
    
    SRC -> DB: 4.6. getUpdatedStok()
    activate DB
    DB --> SRC: 4.7. updatedStokData()
    deactivate DB
    
    SRC --> SC: 4.8. realTimeStokRefreshed()
    deactivate SRC
    
    SC --> SMP: 4.9. updateSuccess()
    SMP --> User: 4.10. showSuccessMessage()
else invalidUpdate
    SC --> SMP: 4.11. updateFailed()
    SMP --> User: 4.12. showErrorMessage()
end

deactivate SC
deactivate SMP

@enduml
