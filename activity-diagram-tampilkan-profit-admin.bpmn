<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_ShowProfitAdmin" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_ShowProfitAdmin">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_SelectPeriod" targetRef="Task_ValidatePeriod" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowProfit" targetRef="Task_ViewProfit" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Admin Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_ShowProfit" name="Start Show Profit">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenProfitMenu" name="Open show profit menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectPeriod" name="Select period (daily/monthly)">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ViewProfit" name="View profit information">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectValidPeriod" name="Select valid period">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_ShowProfit" name="End">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_ShowProfit" targetRef="Task_OpenProfitMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenProfitMenu" targetRef="Task_SelectPeriod" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SelectPeriod" targetRef="Task_ViewProfit" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_ViewProfit" targetRef="EndEvent_ShowProfit" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_ViewProfit" targetRef="EndEvent_ShowProfit" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_SelectValidPeriod" targetRef="Task_SelectPeriod" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_SelectValidPeriod" targetRef="Task_SelectPeriod" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowProfitForm" name="Show profit form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidatePeriod" name="Validate period">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_PeriodValid" name="Period valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_GetSalesData" name="Get sales data">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CalculateRevenue" name="Calculate total revenue">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CalculateCosts" name="Calculate total costs">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CalculateNetProfit" name="Calculate net profit">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
      <bpmn:outgoing>Flow_S8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatDisplay" name="Format profit display">
      <bpmn:incoming>Flow_S8</bpmn:incoming>
      <bpmn:outgoing>Flow_S9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowProfit" name="Show profit information">
      <bpmn:incoming>Flow_S9</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowError" name="Show error message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowProfitForm" targetRef="Task_ValidatePeriod" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidatePeriod" targetRef="Gateway_PeriodValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_PeriodValid" targetRef="Task_GetSalesData" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_PeriodValid" targetRef="Task_ShowError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_GetSalesData" targetRef="Task_CalculateRevenue" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_CalculateRevenue" targetRef="Task_CalculateCosts" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_CalculateCosts" targetRef="Task_CalculateNetProfit" />
    <bpmn:sequenceFlow id="Flow_S8" sourceRef="Task_CalculateNetProfit" targetRef="Task_FormatDisplay" />
    <bpmn:sequenceFlow id="Flow_S9" sourceRef="Task_FormatDisplay" targetRef="Task_ShowProfit" />
  </bpmn:process>
</bpmn:definitions>
