<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_GenerateDailyReport" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_GenerateDailyReport">
    <bpmn:participant id="Participant_Admin" name="Admin" processRef="Process_Admin" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_SelectDate" targetRef="Task_ValidateDate" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowReport" targetRef="Task_ViewReport" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Admin" name="Admin Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_GenerateReport" name="Start Generate Report">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenReportMenu" name="Open daily report menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectDate" name="Select report date">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_ViewReport" name="View daily report">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_SelectValidDate" name="Select valid date">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_GenerateReport" name="End">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_GenerateReport" targetRef="Task_OpenReportMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenReportMenu" targetRef="Task_SelectDate" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SelectDate" targetRef="Task_ViewReport" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_ViewReport" targetRef="EndEvent_GenerateReport" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_ViewReport" targetRef="EndEvent_GenerateReport" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_SelectValidDate" targetRef="Task_SelectDate" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_SelectValidDate" targetRef="Task_SelectDate" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowReportForm" name="Show report form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateDate" name="Validate date">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_DateValid" name="Date valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_GetSalesData" name="Get daily sales data">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_CalculateTotal" name="Calculate total sales">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_FormatReport" name="Format report">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
      <bpmn:outgoing>Flow_S7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowReport" name="Show daily report">
      <bpmn:incoming>Flow_S7</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowError" name="Show error message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowReportForm" targetRef="Task_ValidateDate" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateDate" targetRef="Gateway_DateValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_DateValid" targetRef="Task_GetSalesData" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_DateValid" targetRef="Task_ShowError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_GetSalesData" targetRef="Task_CalculateTotal" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_CalculateTotal" targetRef="Task_FormatReport" />
    <bpmn:sequenceFlow id="Flow_S7" sourceRef="Task_FormatReport" targetRef="Task_ShowReport" />
  </bpmn:process>
</bpmn:definitions>
