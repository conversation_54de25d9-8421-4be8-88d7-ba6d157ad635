@startuml Sequence_Catat_Penjualan_Harian
title Sequence Diagram - Catat Penjualan Harian

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor User
participant ":PenjualanHarianPage" as PHP
participant ":PenjualanController" as PC
participant ":Database" as DB
participant ":DateTimeManager" as DTM

User -> PHP: 1. accessPenjualanHarianPage()
activate PHP

PHP -> PC: 2. loadTodaySalesRecord()
activate PC

PC -> DTM: 2.1. getCurrentDate()
activate DTM
DTM --> PC: 2.2. todayDate()
deactivate DTM

PC -> DB: 2.3. getTodaySalesData(todayDate)
activate DB
DB --> PC: 2.4. todaySalesData()
deactivate DB

PC --> PHP: 2.5. displayTodaySales()
PHP --> User: 2.6. showPenjualanHarianInterface()

User -> PHP: 3. inputSalesData(salesRecord)

PHP -> PC: 4. recordSalesData(salesRecord)

PC -> DTM: 4.1. validateSalesTime(salesRecord)
activate DTM
DTM --> PC: 4.2. timeValidation()
deactivate DTM

alt validSalesTime
    PC -> DB: 4.3. saveSalesRecord(salesRecord)
    activate DB
    DB --> PC: 4.4. salesRecordSaved()
    deactivate DB
    
    PC -> DB: 4.5. updateDailySummary(salesRecord)
    activate DB
    DB --> PC: 4.6. summaryUpdated()
    deactivate DB
    
    PC --> PHP: 4.7. recordingSuccess()
    PHP --> User: 4.8. showSuccessMessage()
    
    PHP -> PC: 4.9. refreshSalesDisplay()
    
    PC -> DB: 4.10. getUpdatedSalesData()
    activate DB
    DB --> PC: 4.11. updatedSalesData()
    deactivate DB
    
    PC --> PHP: 4.12. displayUpdatedSales()
    PHP --> User: 4.13. showUpdatedSalesRecord()
else invalidSalesTime
    PC --> PHP: 4.14. recordingFailed()
    PHP --> User: 4.15. showTimeError()
end

deactivate PC
deactivate PHP

@enduml
