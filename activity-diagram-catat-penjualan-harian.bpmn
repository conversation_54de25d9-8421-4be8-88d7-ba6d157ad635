<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_RecordDailySales" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_RecordDailySales">
    <bpmn:participant id="Participant_Cashier" name="Cashier" processRef="Process_Cashier" />
    <bpmn:participant id="Participant_System" name="System" processRef="Process_System" />
    <bpmn:messageFlow id="MessageFlow_1" sourceRef="Task_EnterSalesData" targetRef="Task_ValidateData" />
    <bpmn:messageFlow id="MessageFlow_2" sourceRef="Task_ShowConfirmation" targetRef="Task_RecordingComplete" />
  </bpmn:collaboration>
  
  <bpmn:process id="Process_Cashier" name="Cashier Process" isExecutable="false">
    <bpmn:startEvent id="StartEvent_RecordSales" name="Start Record Sales">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_OpenSalesMenu" name="Open record sales menu">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_EnterSalesData" name="Enter sales data">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_RecordingComplete" name="Sales recording complete">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_CorrectData" name="Correct incorrect data">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_RecordSales" name="End">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_RecordSales" targetRef="Task_OpenSalesMenu" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_OpenSalesMenu" targetRef="Task_EnterSalesData" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_EnterSalesData" targetRef="Task_RecordingComplete" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_RecordingComplete" targetRef="EndEvent_RecordSales" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_RecordingComplete" targetRef="EndEvent_RecordSales" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_CorrectData" targetRef="Task_EnterSalesData" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_CorrectData" targetRef="Task_EnterSalesData" />
  </bpmn:process>
  
  <bpmn:process id="Process_System" name="System Process" isExecutable="false">
    <bpmn:serviceTask id="Task_ShowSalesForm" name="Show sales form">
      <bpmn:outgoing>Flow_S1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ValidateData" name="Validate sales data">
      <bpmn:incoming>Flow_S1</bpmn:incoming>
      <bpmn:outgoing>Flow_S2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_DataValid" name="Data valid?">
      <bpmn:incoming>Flow_S2</bpmn:incoming>
      <bpmn:outgoing>Flow_S3</bpmn:outgoing>
      <bpmn:outgoing>Flow_S4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_SaveSalesData" name="Save sales data">
      <bpmn:incoming>Flow_S3</bpmn:incoming>
      <bpmn:outgoing>Flow_S5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_LogAudit" name="Log audit trail (Include)">
      <bpmn:incoming>Flow_S5</bpmn:incoming>
      <bpmn:outgoing>Flow_S6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowConfirmation" name="Show success confirmation">
      <bpmn:incoming>Flow_S6</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_ShowError" name="Show error message">
      <bpmn:incoming>Flow_S4</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_S1" sourceRef="Task_ShowSalesForm" targetRef="Task_ValidateData" />
    <bpmn:sequenceFlow id="Flow_S2" sourceRef="Task_ValidateData" targetRef="Gateway_DataValid" />
    <bpmn:sequenceFlow id="Flow_S3" name="Yes" sourceRef="Gateway_DataValid" targetRef="Task_SaveSalesData" />
    <bpmn:sequenceFlow id="Flow_S4" name="No" sourceRef="Gateway_DataValid" targetRef="Task_ShowError" />
    <bpmn:sequenceFlow id="Flow_S5" sourceRef="Task_SaveSalesData" targetRef="Task_LogAudit" />
    <bpmn:sequenceFlow id="Flow_S6" sourceRef="Task_LogAudit" targetRef="Task_ShowConfirmation" />
  </bpmn:process>
</bpmn:definitions>
