<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
  <bpmn2:collaboration id="Collaboration_1">
    <bpmn2:participant id="Participant_Kasir" name="Kasir" processRef="Process_Kasir" />
    <bpmn2:participant id="Participant_Sistem" name="Sistem" processRef="Process_Sistem" />
    <bpmn2:messageFlow id="MessageFlow_1" sourceRef="Task_MasukkanJumlah" targetRef="Task_ValidasiInput" />
    <bpmn2:messageFlow id="MessageFlow_2" sourceRef="Task_TampilkanKonfirmasi" targetRef="Task_SelesaiKelola" />
  </bpmn2:collaboration>
  
  <bpmn2:process id="Process_Kasir" isExecutable="false">
    <bpmn2:startEvent id="StartEvent_1" name="Mulai Kelola Stok">
      <bpmn2:outgoing>SequenceFlow_1</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:task id="Task_BukaMenuStok" name="Buka menu kelola stok">
      <bpmn2:incoming>SequenceFlow_1</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_PilihOperasi" name="Pilih operasi (tambah/kurang)">
      <bpmn2:incoming>SequenceFlow_2</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_3</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_MasukkanJumlah" name="Masukkan jumlah ubi">
      <bpmn2:incoming>SequenceFlow_3</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_4</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_SelesaiKelola" name="Selesai kelola stok">
      <bpmn2:incoming>SequenceFlow_4</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_5</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:endEvent id="EndEvent_1" name="Selesai">
      <bpmn2:incoming>SequenceFlow_5</bpmn2:incoming>
    </bpmn2:endEvent>
    <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="Task_BukaMenuStok" />
    <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="Task_BukaMenuStok" targetRef="Task_PilihOperasi" />
    <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="Task_PilihOperasi" targetRef="Task_MasukkanJumlah" />
    <bpmn2:sequenceFlow id="SequenceFlow_4" sourceRef="Task_MasukkanJumlah" targetRef="Task_SelesaiKelola" />
    <bpmn2:sequenceFlow id="SequenceFlow_5" sourceRef="Task_SelesaiKelola" targetRef="EndEvent_1" />
  </bpmn2:process>
  
  <bpmn2:process id="Process_Sistem" isExecutable="false">
    <bpmn2:task id="Task_ValidasiInput" name="Validasi input">
      <bpmn2:outgoing>SequenceFlow_S1</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:exclusiveGateway id="Gateway_1" name="Input valid?">
      <bpmn2:incoming>SequenceFlow_S1</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S2</bpmn2:outgoing>
      <bpmn2:outgoing>SequenceFlow_S3</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:task id="Task_UpdateStok" name="Update stok ubi matang">
      <bpmn2:incoming>SequenceFlow_S2</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S4</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_CatatAudit" name="Catat log audit (Include)">
      <bpmn2:incoming>SequenceFlow_S4</bpmn2:incoming>
      <bpmn2:outgoing>SequenceFlow_S5</bpmn2:outgoing>
    </bpmn2:task>
    <bpmn2:task id="Task_TampilkanKonfirmasi" name="Tampilkan konfirmasi berhasil">
      <bpmn2:incoming>SequenceFlow_S5</bpmn2:incoming>
    </bpmn2:task>
    <bpmn2:task id="Task_TampilkanError" name="Tampilkan pesan kesalahan">
      <bpmn2:incoming>SequenceFlow_S3</bpmn2:incoming>
    </bpmn2:task>
    <bpmn2:sequenceFlow id="SequenceFlow_S1" sourceRef="Task_ValidasiInput" targetRef="Gateway_1" />
    <bpmn2:sequenceFlow id="SequenceFlow_S2" name="Ya" sourceRef="Gateway_1" targetRef="Task_UpdateStok" />
    <bpmn2:sequenceFlow id="SequenceFlow_S3" name="Tidak" sourceRef="Gateway_1" targetRef="Task_TampilkanError" />
    <bpmn2:sequenceFlow id="SequenceFlow_S4" sourceRef="Task_UpdateStok" targetRef="Task_CatatAudit" />
    <bpmn2:sequenceFlow id="SequenceFlow_S5" sourceRef="Task_CatatAudit" targetRef="Task_TampilkanKonfirmasi" />
  </bpmn2:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1">
      <bpmndi:BPMNShape id="Participant_Kasir_di" bpmnElement="Participant_Kasir" isHorizontal="true">
        <dc:Bounds x="160" y="80" width="1000" height="200" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_Sistem_di" bpmnElement="Participant_Sistem" isHorizontal="true">
        <dc:Bounds x="160" y="320" width="1000" height="280" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="212" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="188" y="205" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_BukaMenuStok_di" bpmnElement="Task_BukaMenuStok">
        <dc:Bounds x="300" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_PilihOperasi_di" bpmnElement="Task_PilihOperasi">
        <dc:Bounds x="450" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_MasukkanJumlah_di" bpmnElement="Task_MasukkanJumlah">
        <dc:Bounds x="600" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_SelesaiKelola_di" bpmnElement="Task_SelesaiKelola">
        <dc:Bounds x="950" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="1102" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1100" y="205" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="Task_ValidasiInput_di" bpmnElement="Task_ValidasiInput">
        <dc:Bounds x="600" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1_di" bpmnElement="Gateway_1" isMarkerVisible="true">
        <dc:Bounds x="745" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="742" y="452" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_UpdateStok_di" bpmnElement="Task_UpdateStok">
        <dc:Bounds x="850" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_CatatAudit_di" bpmnElement="Task_CatatAudit">
        <dc:Bounds x="850" y="480" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_TampilkanKonfirmasi_di" bpmnElement="Task_TampilkanKonfirmasi">
        <dc:Bounds x="950" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_TampilkanError_di" bpmnElement="Task_TampilkanError">
        <dc:Bounds x="600" y="520" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNEdge id="SequenceFlow_1_di" bpmnElement="SequenceFlow_1">
        <di:waypoint x="248" y="180" />
        <di:waypoint x="300" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_2_di" bpmnElement="SequenceFlow_2">
        <di:waypoint x="400" y="180" />
        <di:waypoint x="450" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_3_di" bpmnElement="SequenceFlow_3">
        <di:waypoint x="550" y="180" />
        <di:waypoint x="600" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_4_di" bpmnElement="SequenceFlow_4">
        <di:waypoint x="700" y="180" />
        <di:waypoint x="950" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_5_di" bpmnElement="SequenceFlow_5">
        <di:waypoint x="1050" y="180" />
        <di:waypoint x="1102" y="180" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="SequenceFlow_S1_di" bpmnElement="SequenceFlow_S1">
        <di:waypoint x="700" y="420" />
        <di:waypoint x="745" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S2_di" bpmnElement="SequenceFlow_S2">
        <di:waypoint x="770" y="395" />
        <di:waypoint x="770" y="380" />
        <di:waypoint x="850" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="778" y="385" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S3_di" bpmnElement="SequenceFlow_S3">
        <di:waypoint x="770" y="445" />
        <di:waypoint x="770" y="560" />
        <di:waypoint x="700" y="560" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="772" y="500" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S4_di" bpmnElement="SequenceFlow_S4">
        <di:waypoint x="900" y="420" />
        <di:waypoint x="900" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_S5_di" bpmnElement="SequenceFlow_S5">
        <di:waypoint x="950" y="520" />
        <di:waypoint x="1000" y="520" />
        <di:waypoint x="1000" y="460" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="MessageFlow_1_di" bpmnElement="MessageFlow_1">
        <di:waypoint x="650" y="220" />
        <di:waypoint x="650" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="MessageFlow_2_di" bpmnElement="MessageFlow_2">
        <di:waypoint x="1000" y="380" />
        <di:waypoint x="1000" y="220" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>
