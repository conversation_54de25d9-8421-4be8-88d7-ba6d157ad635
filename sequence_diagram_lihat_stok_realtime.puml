@startuml Sequence_Lihat_Stok_Realtime
title Sequence Diagram - Lihat Stok Real-time

' Color configuration
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam participant {
    BackgroundColor #B3E5FC
    BorderColor #0288D1
}
skinparam sequence {
    ArrowColor #01579B
    LifeLineBorderColor #0288D1
    LifeLineBackgroundColor #E1F5FE
}

actor User
participant ":StokRealTimePage" as SRP
participant ":StokRealTimeController" as SRC
participant ":Database" as DB
participant ":WebSocketManager" as WSM

User -> SRP: 1. accessStokRealTimePage()
activate SRP

SRP -> SRC: 2. initializeRealTimeStok()
activate SRC

SRC -> DB: 2.1. getCurrentStokData()
activate DB
DB --> SRC: 2.2. currentStokData()
deactivate DB

SRC -> WSM: 2.3. establishWebSocketConnection()
activate WSM
WSM --> SRC: 2.4. connectionEstablished()
deactivate WSM

SRC --> SRP: 2.5. displayInitialStok()
SRP --> User: 2.6. showRealTimeStokInterface()

loop Real-time Updates
    DB -> SRC: 3. stokDataChanged()
    activate SRC
    
    SRC -> WSM: 3.1. broadcastStokUpdate()
    activate WSM
    
    WSM -> SRP: 3.2. receiveStokUpdate()
    activate SRP
    
    SRP -> User: 3.3. updateStokDisplay()
    
    deactivate SRP
    deactivate WSM
    deactivate SRC
end

User -> SRP: 4. refreshStokData()
activate SRP

SRP -> SRC: 5. manualRefreshStok()
activate SRC

SRC -> DB: 5.1. getLatestStokData()
activate DB
DB --> SRC: 5.2. latestStokData()
deactivate DB

SRC --> SRP: 5.3. displayRefreshedStok()
SRP --> User: 5.4. showUpdatedStok()

deactivate SRC
deactivate SRP

@enduml
