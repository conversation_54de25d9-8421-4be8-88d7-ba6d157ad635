<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_transaction" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:collaboration id="Collaboration_transaction">
    <bpmn:participant id="Participant_cashier" name="Kasir" processRef="Process_cashier" />
    <bpmn:participant id="Participant_system" name="Sistem" processRef="Process_system" />
    <bpmn:messageFlow id="Flow_trans_1" sourceRef="Activity_enter_qty" targetRef="Activity_validate_stock" />
    <bpmn:messageFlow id="Flow_trans_2" sourceRef="Activity_show_total" targetRef="Activity_receive_payment" />
    <bpmn:messageFlow id="Flow_trans_3" sourceRef="Activity_show_change" targetRef="Activity_complete_trans" />
  </bpmn:collaboration>
  <bpmn:process id="Process_cashier" isExecutable="true">
    <bpmn:startEvent id="StartEvent_trans" name="Mulai Transaksi">
      <bpmn:outgoing>Flow_start_trans</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Activity_open_trans" name="Buka menu transaksi">
      <bpmn:incoming>Flow_start_trans</bpmn:incoming>
      <bpmn:outgoing>Flow_open_enter</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_enter_qty" name="Masukkan jumlah ubi">
      <bpmn:incoming>Flow_open_enter</bpmn:incoming>
      <bpmn:incoming>Flow_retry</bpmn:incoming>
      <bpmn:outgoing>Flow_enter_receive</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_receive_payment" name="Terima uang dari pelanggan">
      <bpmn:incoming>Flow_enter_receive</bpmn:incoming>
      <bpmn:outgoing>Flow_receive_complete</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_complete_trans" name="Selesai transaksi">
      <bpmn:incoming>Flow_receive_complete</bpmn:incoming>
      <bpmn:outgoing>Flow_complete_end</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_try_again" name="Coba lagi dengan jumlah yang valid">
      <bpmn:incoming>Flow_error_retry</bpmn:incoming>
      <bpmn:outgoing>Flow_retry</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="EndEvent_trans" name="Selesai">
      <bpmn:incoming>Flow_complete_end</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_start_trans" sourceRef="StartEvent_trans" targetRef="Activity_open_trans" />
    <bpmn:sequenceFlow id="Flow_open_enter" sourceRef="Activity_open_trans" targetRef="Activity_enter_qty" />
    <bpmn:sequenceFlow id="Flow_enter_receive" sourceRef="Activity_enter_qty" targetRef="Activity_receive_payment" />
    <bpmn:sequenceFlow id="Flow_receive_complete" sourceRef="Activity_receive_payment" targetRef="Activity_complete_trans" />
    <bpmn:sequenceFlow id="Flow_complete_end" sourceRef="Activity_complete_trans" targetRef="EndEvent_trans" />
    <bpmn:sequenceFlow id="Flow_retry" sourceRef="Activity_try_again" targetRef="Activity_enter_qty" />
    <bpmn:sequenceFlow id="Flow_error_retry" sourceRef="Activity_try_again" targetRef="Activity_enter_qty" />
  </bpmn:process>
  <bpmn:process id="Process_system" isExecutable="false">
    <bpmn:task id="Activity_show_form" name="Tampilkan form transaksi">
      <bpmn:outgoing>Flow_form_validate</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_validate_stock" name="Validasi stok tersedia">
      <bpmn:incoming>Flow_form_validate</bpmn:incoming>
      <bpmn:outgoing>Flow_validate_gateway</bpmn:outgoing>
    </bpmn:task>
    <bpmn:exclusiveGateway id="Gateway_stock" name="Stok cukup?">
      <bpmn:incoming>Flow_validate_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_stock_yes</bpmn:outgoing>
      <bpmn:outgoing>Flow_stock_no</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:task id="Activity_calc_total" name="Hitung total harga">
      <bpmn:incoming>Flow_stock_yes</bpmn:incoming>
      <bpmn:outgoing>Flow_calc_show</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_show_total" name="Tampilkan total harga">
      <bpmn:incoming>Flow_calc_show</bpmn:incoming>
      <bpmn:outgoing>Flow_show_change</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_calc_change" name="Hitung kembalian">
      <bpmn:incoming>Flow_show_change</bpmn:incoming>
      <bpmn:outgoing>Flow_change_update</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_update_stock" name="Update stok ubi">
      <bpmn:incoming>Flow_change_update</bpmn:incoming>
      <bpmn:outgoing>Flow_update_save</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_save_trans" name="Simpan data transaksi">
      <bpmn:incoming>Flow_update_save</bpmn:incoming>
      <bpmn:outgoing>Flow_save_show</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_show_change" name="Tampilkan kembalian">
      <bpmn:incoming>Flow_save_show</bpmn:incoming>
    </bpmn:task>
    <bpmn:task id="Activity_show_error" name="Tampilkan pesan stok tidak cukup">
      <bpmn:incoming>Flow_stock_no</bpmn:incoming>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_form_validate" sourceRef="Activity_show_form" targetRef="Activity_validate_stock" />
    <bpmn:sequenceFlow id="Flow_validate_gateway" sourceRef="Activity_validate_stock" targetRef="Gateway_stock" />
    <bpmn:sequenceFlow id="Flow_stock_yes" name="Ya" sourceRef="Gateway_stock" targetRef="Activity_calc_total" />
    <bpmn:sequenceFlow id="Flow_stock_no" name="Tidak" sourceRef="Gateway_stock" targetRef="Activity_show_error" />
    <bpmn:sequenceFlow id="Flow_calc_show" sourceRef="Activity_calc_total" targetRef="Activity_show_total" />
    <bpmn:sequenceFlow id="Flow_show_change" sourceRef="Activity_show_total" targetRef="Activity_calc_change" />
    <bpmn:sequenceFlow id="Flow_change_update" sourceRef="Activity_calc_change" targetRef="Activity_update_stock" />
    <bpmn:sequenceFlow id="Flow_update_save" sourceRef="Activity_update_stock" targetRef="Activity_save_trans" />
    <bpmn:sequenceFlow id="Flow_save_show" sourceRef="Activity_save_trans" targetRef="Activity_show_change" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_trans">
    <bpmndi:BPMNPlane id="BPMNPlane_trans" bpmnElement="Collaboration_transaction">
      <bpmndi:BPMNShape id="Participant_cashier_di" bpmnElement="Participant_cashier" isHorizontal="true">
        <dc:Bounds x="129" y="80" width="1201" height="250" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_complete_end_di" bpmnElement="Flow_complete_end">
        <di:waypoint x="1170" y="177" />
        <di:waypoint x="1252" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_receive_complete_di" bpmnElement="Flow_receive_complete">
        <di:waypoint x="1020" y="177" />
        <di:waypoint x="1070" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_enter_receive_di" bpmnElement="Flow_enter_receive">
        <di:waypoint x="570" y="177" />
        <di:waypoint x="920" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_open_enter_di" bpmnElement="Flow_open_enter">
        <di:waypoint x="370" y="177" />
        <di:waypoint x="420" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_start_trans_di" bpmnElement="Flow_start_trans">
        <di:waypoint x="215" y="177" />
        <di:waypoint x="270" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_retry_di" bpmnElement="Flow_retry">
        <di:waypoint x="720" y="177" />
        <di:waypoint x="570" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_trans_di" bpmnElement="StartEvent_trans">
        <dc:Bounds x="179" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="202" width="76" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_open_trans_di" bpmnElement="Activity_open_trans">
        <dc:Bounds x="270" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_enter_qty_di" bpmnElement="Activity_enter_qty">
        <dc:Bounds x="420" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_receive_payment_di" bpmnElement="Activity_receive_payment">
        <dc:Bounds x="920" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_complete_trans_di" bpmnElement="Activity_complete_trans">
        <dc:Bounds x="1070" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_try_again_di" bpmnElement="Activity_try_again">
        <dc:Bounds x="720" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_trans_di" bpmnElement="EndEvent_trans">
        <dc:Bounds x="1252" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1250" y="202" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Participant_system_di" bpmnElement="Participant_system" isHorizontal="true">
        <dc:Bounds x="129" y="380" width="1201" height="300" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_save_show_di" bpmnElement="Flow_save_show">
        <di:waypoint x="1170" y="530" />
        <di:waypoint x="1220" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_update_save_di" bpmnElement="Flow_update_save">
        <di:waypoint x="1020" y="530" />
        <di:waypoint x="1070" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_change_update_di" bpmnElement="Flow_change_update">
        <di:waypoint x="870" y="530" />
        <di:waypoint x="920" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_show_change_di" bpmnElement="Flow_show_change">
        <di:waypoint x="720" y="530" />
        <di:waypoint x="770" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_calc_show_di" bpmnElement="Flow_calc_show">
        <di:waypoint x="570" y="530" />
        <di:waypoint x="620" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_stock_no_di" bpmnElement="Flow_stock_no">
        <di:waypoint x="445" y="455" />
        <di:waypoint x="445" y="620" />
        <di:waypoint x="620" y="620" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="447" y="535" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_stock_yes_di" bpmnElement="Flow_stock_yes">
        <di:waypoint x="470" y="430" />
        <di:waypoint x="520" y="430" />
        <di:waypoint x="520" y="490" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="490" y="412" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_validate_gateway_di" bpmnElement="Flow_validate_gateway">
        <di:waypoint x="370" y="430" />
        <di:waypoint x="420" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_form_validate_di" bpmnElement="Flow_form_validate">
        <di:waypoint x="220" y="430" />
        <di:waypoint x="270" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_show_form_di" bpmnElement="Activity_show_form">
        <dc:Bounds x="170" y="390" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_validate_stock_di" bpmnElement="Activity_validate_stock">
        <dc:Bounds x="270" y="390" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_stock_di" bpmnElement="Gateway_stock" isMarkerVisible="true">
        <dc:Bounds x="420" y="405" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="413" y="462" width="64" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_calc_total_di" bpmnElement="Activity_calc_total">
        <dc:Bounds x="470" y="490" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_show_total_di" bpmnElement="Activity_show_total">
        <dc:Bounds x="620" y="490" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_calc_change_di" bpmnElement="Activity_calc_change">
        <dc:Bounds x="770" y="490" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_update_stock_di" bpmnElement="Activity_update_stock">
        <dc:Bounds x="920" y="490" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_save_trans_di" bpmnElement="Activity_save_trans">
        <dc:Bounds x="1070" y="490" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_show_change_di" bpmnElement="Activity_show_change">
        <dc:Bounds x="1220" y="490" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_show_error_di" bpmnElement="Activity_show_error">
        <dc:Bounds x="620" y="580" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_trans_1_di" bpmnElement="Flow_trans_1">
        <di:waypoint x="470" y="217" />
        <di:waypoint x="470" y="304" />
        <di:waypoint x="320" y="304" />
        <di:waypoint x="320" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_trans_2_di" bpmnElement="Flow_trans_2">
        <di:waypoint x="670" y="490" />
        <di:waypoint x="670" y="350" />
        <di:waypoint x="970" y="350" />
        <di:waypoint x="970" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_trans_3_di" bpmnElement="Flow_trans_3">
        <di:waypoint x="1270" y="490" />
        <di:waypoint x="1270" y="350" />
        <di:waypoint x="1120" y="350" />
        <di:waypoint x="1120" y="217" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
